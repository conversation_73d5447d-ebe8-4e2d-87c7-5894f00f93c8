#!/usr/bin/env python3
"""
大规模数据集测试：20x10 FJSP实例
对比三种算法在更具挑战性的问题上的表现
"""

import time
import torch
import numpy as np
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from traditional_branch_price import TraditionalBranchPrice
from column_generator import ColumnGenerator
from master_solver import MasterSolver
from params import configs
from data_utils import text_to_matrix
from common_utils import heuristic_select_action
import copy

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (15, 10)
sns.set_style("whitegrid")

# 全局设备配置
device = 'cpu'

def load_trained_model(model_path, configs, device):
    """加载训练好的模型"""
    if not os.path.exists(model_path):
        print(f"Model file {model_path} not found!")
        return None
    
    try:
        model = DANIEL(configs)
        model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
        model = model.to(device)
        model.eval()
        print(f"Model loaded successfully from {model_path}")
        return model
    except Exception as e:
        print(f"Failed to load model: {e}")
        return None

def run_traditional_branch_price_20x10(job_length, op_pt, configs):
    """运行传统分支定价方法（针对20x10优化）"""
    print("    Running Traditional Branch&Price...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    try:
        traditional_solver = TraditionalBranchPrice(configs)
        # 增加迭代次数和列数，适应更大规模问题
        result = traditional_solver.solve(job_length, op_pt, max_iterations=20, max_columns_per_iter=12)
        
        total_time = time.time() - start_time
        result['time'] = total_time
        result['method'] = 'Traditional_BranchPrice_20x10'
        
        print(f"      Traditional B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {result['iterations']})")
        return result
        
    except Exception as e:
        print(f"      Traditional B&P failed: {e}")
        return {
            'makespan': float('inf'),
            'time': time.time() - start_time,
            'method': 'Traditional_BranchPrice_Failed',
            'iterations': 0
        }

def run_pure_drl_20x10(job_length, op_pt, configs, model, device):
    """运行纯DRL算法（针对20x10优化）"""
    print("    Running Pure DRL...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    try:
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        schedule = []
        step = 0
        max_steps = configs.n_op
        
        with torch.no_grad():
            while not env.done()[0] and step < max_steps:
                env_state = env.state
                
                # 确保tensor在正确设备上
                fea_j = env_state.fea_j_tensor.to(device)
                op_mask = env_state.op_mask_tensor.to(device)
                candidate = env_state.candidate_tensor.to(device)
                fea_m = env_state.fea_m_tensor.to(device)
                mch_mask = env_state.mch_mask_tensor.to(device)
                comp_idx = env_state.comp_idx_tensor.to(device)
                dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(device)
                fea_pairs = env_state.fea_pairs_tensor.to(device)
                
                # 模型推理
                pi, _ = model(fea_j, op_mask, candidate, fea_m,
                             mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 贪婪选择最佳动作
                action = torch.argmax(pi, dim=1).item()
                
                # 记录调度信息
                chosen_job = action // env.number_of_machines
                chosen_mch = action % env.number_of_machines
                chosen_op = env.candidate[0, chosen_job]
                
                start_time_op = max(env.candidate_free_time[0, chosen_job],
                               env.mch_free_time[0, chosen_mch])
                processing_time = env.true_op_pt[0, chosen_op, chosen_mch]
                end_time = start_time_op + processing_time
                
                schedule.append({
                    "op_id": int(chosen_op),
                    "job_id": int(chosen_job),
                    "mch_id": int(chosen_mch),
                    "start_time": float(start_time_op),
                    "end_time": float(end_time),
                    "processing_time": float(processing_time)
                })
                
                # 执行动作
                state, reward, done = env.step(np.array([action]))
                step += 1
        
        total_time = time.time() - start_time
        
        if schedule:
            makespan = max(task['end_time'] for task in schedule)
            result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': 'Pure_DRL_20x10',
                'time': total_time
            }
            print(f"      Pure DRL: {makespan:.3f} (time: {total_time:.3f}s)")
            return result
        else:
            print(f"      Pure DRL: Failed to generate schedule")
            return {
                'makespan': float('inf'),
                'schedule': [],
                'method': 'Pure_DRL_Failed',
                'time': total_time
            }
            
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        return {
            'makespan': float('inf'),
            'schedule': [],
            'method': 'Pure_DRL_Error',
            'time': time.time() - start_time
        }

def run_improved_drl_branch_price_20x10(job_length, op_pt, configs, model, device):
    """运行改进的DRL+分支定价算法（针对20x10优化）"""
    print("    Running Improved DRL + Branch&Price...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    configs.max_iterations = 20  # 增加迭代次数
    configs.cg_topk = 12  # 增加列生成数量
    configs.device = device
    
    try:
        # 确保模型在正确设备上
        model = model.to(device)
        model.eval()
        
        column_generator = ColumnGenerator(model, configs)
        master_solver = MasterSolver(configs)
        
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        iteration = 0
        total_columns = 0
        previous_obj = None
        
        while iteration < configs.max_iterations:
            iteration += 1
            
            # 获取对偶价格
            dual_values = master_solver.get_dual_prices() if iteration > 1 else None
            
            # 使用改进的列生成（包含top-k + entropy sampling）
            new_columns = column_generator.generate_columns(
                env, dual_values=dual_values, top_k=configs.cg_topk
            )
            
            if not new_columns:
                print(f"      No new columns at iteration {iteration}")
                break
            
            total_columns += len(new_columns)
            master_solver.add_columns(new_columns)
            
            # 求解主问题
            solution, min_reduced_cost = master_solver.solve()
            
            if solution is None:
                print(f"      Master problem infeasible at iteration {iteration}")
                break
            
            # 计算实际的最小reduced cost
            actual_min_rc = min(col.get('reduced_cost', 0.0) for col in new_columns)
            
            # 改进的收敛判断
            current_obj = solution[0]['cost'] if solution else float('inf')
            
            # 基于目标值改善的收敛判断
            if previous_obj is not None and previous_obj > 0:
                improvement = (previous_obj - current_obj) / previous_obj
                if improvement < 0.001:  # 0.1%改善阈值
                    print(f"      Converged by objective improvement at iteration {iteration}")
                    break
            
            previous_obj = current_obj
            
            # 基于reduced cost的收敛判断
            if actual_min_rc >= -0.01:  # 调整收敛阈值
                print(f"      Converged by reduced cost at iteration {iteration}")
                break
        
        total_time = time.time() - start_time
        
        # 获取最终解
        final_solution = master_solver.get_solution_columns()
        if final_solution:
            best_makespan = min(col['makespan'] for col in final_solution if 'makespan' in col)
            best_schedule = next(col['schedule'] for col in final_solution 
                               if 'makespan' in col and col['makespan'] == best_makespan)
            
            result = {
                'makespan': best_makespan,
                'schedule': best_schedule,
                'method': f'Improved_DRL_BranchPrice_20x10_{iteration}iter',
                'time': total_time,
                'iterations': iteration,
                'total_columns': total_columns
            }
            
            print(f"      Improved DRL+B&P: {best_makespan:.3f} (time: {total_time:.3f}s, iters: {iteration})")
            return result
        else:
            print(f"      Improved DRL+B&P: No solution found")
            return {
                'makespan': float('inf'),
                'schedule': [],
                'method': 'Improved_DRL_BranchPrice_NoSolution',
                'time': total_time,
                'iterations': iteration,
                'total_columns': total_columns
            }
            
    except Exception as e:
        print(f"      Improved DRL+B&P failed: {e}")
        return {
            'makespan': float('inf'),
            'schedule': [],
            'method': f'Improved_DRL_BranchPrice_Error',
            'time': time.time() - start_time,
            'iterations': 0,
            'total_columns': 0
        }

def test_single_instance_20x10(instance_path, model, device):
    """在单个20x10实例上测试三种算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")

    with open(instance_path, 'r') as f:
        lines = f.readlines()

    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]

    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")

    results = {}

    # 算法1: 传统分支定价
    try:
        results['traditional'] = run_traditional_branch_price_20x10(job_length, op_pt, configs)
    except Exception as e:
        print(f"      Traditional B&P failed: {e}")
        results['traditional'] = {'makespan': float('inf'), 'time': 0, 'method': 'Traditional_Failed'}

    # 算法2: 纯DRL
    try:
        results['drl'] = run_pure_drl_20x10(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        results['drl'] = {'makespan': float('inf'), 'time': 0, 'method': 'Pure_DRL_Failed'}

    # 算法3: 改进的DRL+分支定价
    try:
        results['hybrid'] = run_improved_drl_branch_price_20x10(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Improved DRL+B&P failed: {e}")
        results['hybrid'] = {'makespan': float('inf'), 'time': 0, 'method': 'Improved_DRL_BranchPrice_Failed'}

    # 分析结果
    traditional_makespan = results['traditional']['makespan']
    drl_makespan = results['drl']['makespan']
    hybrid_makespan = results['hybrid']['makespan']

    traditional_time = results['traditional']['time']
    drl_time = results['drl']['time']
    hybrid_time = results['hybrid']['time']

    # 计算改善百分比
    improvements = {}
    if traditional_makespan != float('inf'):
        if drl_makespan != float('inf'):
            improvements['drl_vs_traditional'] = (traditional_makespan - drl_makespan) / traditional_makespan * 100
        if hybrid_makespan != float('inf'):
            improvements['hybrid_vs_traditional'] = (traditional_makespan - hybrid_makespan) / traditional_makespan * 100

    if drl_makespan != float('inf') and hybrid_makespan != float('inf'):
        improvements['hybrid_vs_drl'] = (drl_makespan - hybrid_makespan) / drl_makespan * 100

    # 找出最佳算法
    valid_results = [(name, res['makespan']) for name, res in results.items() if res['makespan'] != float('inf')]
    best_algorithm = min(valid_results, key=lambda x: x[1])[0] if valid_results else 'None'

    print(f"  Results:")
    print(f"    Traditional B&P:     {traditional_makespan:.3f} ({traditional_time:.3f}s)")
    print(f"    Pure DRL:            {drl_makespan:.3f} ({drl_time:.3f}s)")
    print(f"    Improved DRL+B&P:    {hybrid_makespan:.3f} ({hybrid_time:.3f}s)")
    print(f"    Best Algorithm:      {best_algorithm}")

    if improvements:
        print(f"  Improvements:")
        for key, value in improvements.items():
            print(f"    {key}: {value:.2f}%")

    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'traditional_makespan': traditional_makespan,
        'drl_makespan': drl_makespan,
        'hybrid_makespan': hybrid_makespan,
        'traditional_time': traditional_time,
        'drl_time': drl_time,
        'hybrid_time': hybrid_time,
        'best_algorithm': best_algorithm,
        'improvements': improvements,
        'traditional_method': results['traditional']['method'],
        'drl_method': results['drl']['method'],
        'hybrid_method': results['hybrid']['method'],
        'hybrid_iterations': results['hybrid'].get('iterations', 0),
        'hybrid_columns': results['hybrid'].get('total_columns', 0),
        'traditional_iterations': results['traditional'].get('iterations', 0)
    }

def create_20x10_visualization(results):
    """创建20x10数据集的对比可视化图表"""
    if not results:
        print("No results to visualize")
        return

    # 过滤有效结果
    valid_results = [r for r in results if all(r[f'{alg}_makespan'] != float('inf')
                                              for alg in ['traditional', 'drl', 'hybrid'])]

    if not valid_results:
        print("No valid results to visualize")
        return

    # 准备数据
    instances = [r['instance'] for r in valid_results]
    traditional_makespans = [r['traditional_makespan'] for r in valid_results]
    drl_makespans = [r['drl_makespan'] for r in valid_results]
    hybrid_makespans = [r['hybrid_makespan'] for r in valid_results]

    traditional_times = [r['traditional_time'] for r in valid_results]
    drl_times = [r['drl_time'] for r in valid_results]
    hybrid_times = [r['hybrid_time'] for r in valid_results]

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 颜色配置
    colors = {
        'traditional': '#FF6B6B',
        'drl': '#4ECDC4',
        'hybrid': '#45B7D1'
    }

    # 1. Makespan对比
    x = np.arange(len(valid_results))
    width = 0.25

    bars1 = ax1.bar(x - width, traditional_makespans, width,
                   label='Traditional B&P', color=colors['traditional'], alpha=0.8)
    bars2 = ax1.bar(x, drl_makespans, width,
                   label='Pure DRL', color=colors['drl'], alpha=0.8)
    bars3 = ax1.bar(x + width, hybrid_makespans, width,
                   label='Improved DRL+B&P', color=colors['hybrid'], alpha=0.8)

    ax1.set_xlabel('Test Instance')
    ax1.set_ylabel('Makespan')
    ax1.set_title('Makespan Comparison: 20x10 Dataset', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f"#{i+1}" for i in range(len(valid_results))])
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 时间对比（对数刻度）
    bars4 = ax2.bar(x - width, traditional_times, width,
                   label='Traditional B&P', color=colors['traditional'], alpha=0.8)
    bars5 = ax2.bar(x, drl_times, width,
                   label='Pure DRL', color=colors['drl'], alpha=0.8)
    bars6 = ax2.bar(x + width, hybrid_times, width,
                   label='Improved DRL+B&P', color=colors['hybrid'], alpha=0.8)

    ax2.set_xlabel('Test Instance')
    ax2.set_ylabel('Time (seconds)')
    ax2.set_title('Computation Time Comparison: 20x10 Dataset', fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels([f"#{i+1}" for i in range(len(valid_results))])
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')

    # 3. DRL vs DRL+B&P 质量对比
    drl_vs_hybrid_improvement = []
    for r in valid_results:
        if 'hybrid_vs_drl' in r['improvements']:
            drl_vs_hybrid_improvement.append(r['improvements']['hybrid_vs_drl'])
        else:
            drl_vs_hybrid_improvement.append(0)

    bars7 = ax3.bar(x, drl_vs_hybrid_improvement, color='#96CEB4', alpha=0.8)
    ax3.set_xlabel('Test Instance')
    ax3.set_ylabel('Improvement (%)')
    ax3.set_title('DRL+B&P vs Pure DRL Quality Improvement', fontweight='bold')
    ax3.set_xticks(x)
    ax3.set_xticklabels([f"#{i+1}" for i in range(len(valid_results))])
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)

    # 4. 算法胜率统计
    algorithm_wins = {'Traditional B&P': 0, 'Pure DRL': 0, 'Improved DRL+B&P': 0}
    for r in valid_results:
        best_alg = r['best_algorithm']
        if best_alg == 'traditional':
            algorithm_wins['Traditional B&P'] += 1
        elif best_alg == 'drl':
            algorithm_wins['Pure DRL'] += 1
        elif best_alg == 'hybrid':
            algorithm_wins['Improved DRL+B&P'] += 1

    algorithms = list(algorithm_wins.keys())
    wins = list(algorithm_wins.values())
    colors_pie = [colors['traditional'], colors['drl'], colors['hybrid']]

    ax4.pie(wins, labels=algorithms, colors=colors_pie, autopct='%1.1f%%', startangle=90)
    ax4.set_title('Algorithm Win Rate: 20x10 Dataset', fontweight='bold')

    plt.suptitle('Large Scale Comparison: 20x10 FJSP Dataset (Top-k + Entropy Sampling)',
                fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    if not os.path.exists('./comparison_results'):
        os.makedirs('./comparison_results')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/large_scale_20x10_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"20x10 comparison visualization saved: {filename}")

    plt.show()
    return fig

def save_20x10_results_to_csv(results):
    """保存20x10结果到CSV文件"""
    if not results:
        print("No results to save")
        return

    df = pd.DataFrame(results)

    if not os.path.exists('./comparison_results'):
        os.makedirs('./comparison_results')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/large_scale_20x10_{timestamp}.csv"
    df.to_csv(filename, index=False)
    print(f"Results saved to: {filename}")
    return filename

def run_large_scale_20x10_comparison():
    """运行大规模20x10数据集的完整对比测试"""
    print("=== Large Scale Comparison: 20x10 FJSP Dataset ===")
    print("Methods:")
    print("  1. Traditional Branch & Price")
    print("  2. Pure DRL")
    print("  3. Improved DRL + Branch & Price (Top-k + Entropy Sampling)")
    print("Dataset: SD2 20x10+mix (20 jobs, 10 machines, 100 operations)")
    print("="*70)

    # 加载训练好的模型
    model_path = './trained_network/SD2/20x10+mix.pth'
    model = load_trained_model(model_path, configs, device)

    if model is None:
        print("Failed to load model, exiting...")
        return []

    # 测试数据路径
    data_path = './data/SD2/20x10+mix'

    if not os.path.exists(data_path):
        print(f"Data path {data_path} not found!")
        return []

    # 获取测试文件
    test_files = [f for f in os.listdir(data_path) if f.endswith('.fjs')]
    test_files.sort()

    if not test_files:
        print(f"No .fjs files found in {data_path}")
        return []

    print(f"Found {len(test_files)} test instances")

    # 限制测试数量（20x10问题更复杂，测试较少实例）
    max_tests = 8
    test_files = test_files[:max_tests]
    print(f"Testing first {len(test_files)} instances...")

    all_results = []

    for i, filename in enumerate(test_files):
        print(f"\n[{i+1}/{len(test_files)}] Processing {filename}...")
        instance_path = os.path.join(data_path, filename)

        try:
            result = test_single_instance_20x10(instance_path, model, device)
            all_results.append(result)
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            continue

    # 分析总体结果
    if all_results:
        valid_results = [r for r in all_results if all(r[f'{alg}_makespan'] != float('inf')
                                                      for alg in ['traditional', 'drl', 'hybrid'])]

        if valid_results:
            print(f"\n=== Summary Statistics (20x10 Dataset) ===")
            print(f"Total instances tested: {len(all_results)}")
            print(f"Valid results: {len(valid_results)}")

            # 计算统计数据
            avg_traditional = np.mean([r['traditional_makespan'] for r in valid_results])
            avg_drl = np.mean([r['drl_makespan'] for r in valid_results])
            avg_hybrid = np.mean([r['hybrid_makespan'] for r in valid_results])

            avg_traditional_time = np.mean([r['traditional_time'] for r in valid_results])
            avg_drl_time = np.mean([r['drl_time'] for r in valid_results])
            avg_hybrid_time = np.mean([r['hybrid_time'] for r in valid_results])

            avg_traditional_iters = np.mean([r['traditional_iterations'] for r in valid_results])
            avg_hybrid_iters = np.mean([r['hybrid_iterations'] for r in valid_results])

            # 胜率统计
            wins = {'traditional': 0, 'drl': 0, 'hybrid': 0}
            for r in valid_results:
                wins[r['best_algorithm']] += 1

            print(f"\nAverage Makespan:")
            print(f"  Traditional B&P:       {avg_traditional:.3f}")
            print(f"  Pure DRL:              {avg_drl:.3f}")
            print(f"  Improved DRL+B&P:      {avg_hybrid:.3f}")

            print(f"\nAverage Time:")
            print(f"  Traditional B&P:       {avg_traditional_time:.3f}s")
            print(f"  Pure DRL:              {avg_drl_time:.3f}s")
            print(f"  Improved DRL+B&P:      {avg_hybrid_time:.3f}s")

            print(f"\nAverage Iterations:")
            print(f"  Traditional B&P:       {avg_traditional_iters:.1f}")
            print(f"  Improved DRL+B&P:      {avg_hybrid_iters:.1f}")

            print(f"\nWin Rate:")
            total_valid = len(valid_results)
            print(f"  Traditional B&P:       {wins['traditional']}/{total_valid} ({wins['traditional']/total_valid*100:.1f}%)")
            print(f"  Pure DRL:              {wins['drl']}/{total_valid} ({wins['drl']/total_valid*100:.1f}%)")
            print(f"  Improved DRL+B&P:      {wins['hybrid']}/{total_valid} ({wins['hybrid']/total_valid*100:.1f}%)")

            # 计算平均改善
            if avg_traditional > 0:
                drl_improvement = (avg_traditional - avg_drl) / avg_traditional * 100
                hybrid_improvement = (avg_traditional - avg_hybrid) / avg_traditional * 100
                print(f"\nAverage Improvement vs Traditional B&P:")
                print(f"  Pure DRL:              {drl_improvement:.2f}%")
                print(f"  Improved DRL+B&P:      {hybrid_improvement:.2f}%")

            if avg_drl > 0:
                hybrid_vs_drl = (avg_drl - avg_hybrid) / avg_drl * 100
                print(f"  Improved DRL+B&P vs Pure DRL: {hybrid_vs_drl:.2f}%")

            # 时间效率分析
            drl_speedup = avg_traditional_time / avg_drl_time if avg_drl_time > 0 else float('inf')
            hybrid_speedup = avg_traditional_time / avg_hybrid_time if avg_hybrid_time > 0 else float('inf')

            print(f"\nTime Efficiency vs Traditional B&P:")
            print(f"  Pure DRL speedup:      {drl_speedup:.1f}x")
            print(f"  DRL+B&P speedup:       {hybrid_speedup:.1f}x")

            if avg_drl_time > 0:
                hybrid_vs_drl_time = avg_hybrid_time / avg_drl_time
                print(f"  DRL+B&P vs Pure DRL:   {hybrid_vs_drl_time:.1f}x slower")

        # 保存结果和创建可视化
        save_20x10_results_to_csv(all_results)
        if valid_results:
            create_20x10_visualization(valid_results)

    return all_results

if __name__ == "__main__":
    results = run_large_scale_20x10_comparison()

    if results:
        print(f"\n=== Large Scale Test Completed ===")
        print(f"Total results: {len(results)}")
        print("Check ./comparison_results/ for detailed results and visualizations")
        print("\nKey Findings for 20x10 Dataset:")
        print("- Tested on more challenging 20x10 instances (100 operations)")
        print("- Top-k + Entropy Sampling strategy performance on larger scale")
        print("- Comprehensive scalability analysis completed")
    else:
        print("No results generated")
