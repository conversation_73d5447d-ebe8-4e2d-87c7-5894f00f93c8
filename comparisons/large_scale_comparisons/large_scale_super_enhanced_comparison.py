#!/usr/bin/env python3
"""
大规模Super Enhanced DRL+分支定价 vs Pure DRL对比测试
测试规模：30x10 和 40x10
目标：验证Super Enhanced算法在更大规模问题上的扩展性和优势
"""

import time
import torch
import numpy as np
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from super_enhanced_column_generator import SuperEnhancedColumnGenerator
from enhanced_master_solver import EnhancedMasterSolver
from params import configs
from data_utils import text_to_matrix
import copy

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (16, 12)
sns.set_style("whitegrid")

# 全局设备配置
device = 'cpu'

def load_trained_model(model_path, configs, device):
    """加载训练好的模型"""
    if not os.path.exists(model_path):
        print(f"Model file {model_path} not found!")
        return None
    
    try:
        model = DANIEL(configs)
        model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
        model = model.to(device)
        model.eval()
        print(f"Model loaded successfully from {model_path}")
        return model
    except Exception as e:
        print(f"Failed to load model: {e}")
        return None

def run_pure_drl_large_scale(job_length, op_pt, configs, model, device):
    """运行纯DRL算法（大规模优化版）"""
    print("    Running Pure DRL (Large Scale)...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    try:
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        schedule = []
        step = 0
        max_steps = configs.n_op
        
        with torch.no_grad():
            while not env.done()[0] and step < max_steps:
                env_state = env.state
                
                # 确保tensor在正确设备上
                fea_j = env_state.fea_j_tensor.to(device)
                op_mask = env_state.op_mask_tensor.to(device)
                candidate = env_state.candidate_tensor.to(device)
                fea_m = env_state.fea_m_tensor.to(device)
                mch_mask = env_state.mch_mask_tensor.to(device)
                comp_idx = env_state.comp_idx_tensor.to(device)
                dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(device)
                fea_pairs = env_state.fea_pairs_tensor.to(device)
                
                # 模型推理
                pi, _ = model(fea_j, op_mask, candidate, fea_m,
                             mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 贪婪选择最佳动作
                action = torch.argmax(pi, dim=1).item()
                
                # 记录调度信息
                chosen_job = action // env.number_of_machines
                chosen_mch = action % env.number_of_machines
                chosen_op = env.candidate[0, chosen_job]
                
                start_time_op = max(env.candidate_free_time[0, chosen_job],
                               env.mch_free_time[0, chosen_mch])
                processing_time = env.true_op_pt[0, chosen_op, chosen_mch]
                end_time = start_time_op + processing_time
                
                schedule.append({
                    "op_id": int(chosen_op),
                    "job_id": int(chosen_job),
                    "mch_id": int(chosen_mch),
                    "start_time": float(start_time_op),
                    "end_time": float(end_time),
                    "processing_time": float(processing_time)
                })
                
                # 执行动作
                state, reward, done = env.step(np.array([action]))
                step += 1
        
        total_time = time.time() - start_time
        
        if schedule:
            makespan = max(task['end_time'] for task in schedule)
            result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': f'Pure_DRL_{configs.n_j}x{configs.n_m}',
                'time': total_time,
                'steps': step,
                'problem_size': f"{configs.n_j}x{configs.n_m}",
                'operations': configs.n_op
            }
            print(f"      Pure DRL: {makespan:.3f} (time: {total_time:.3f}s, ops: {configs.n_op})")
            return result
        else:
            return {
                'makespan': float('inf'),
                'schedule': [],
                'method': f'Pure_DRL_{configs.n_j}x{configs.n_m}_Failed',
                'time': total_time,
                'steps': step,
                'problem_size': f"{configs.n_j}x{configs.n_m}",
                'operations': configs.n_op
            }
            
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        return {
            'makespan': float('inf'),
            'schedule': [],
            'method': f'Pure_DRL_{configs.n_j}x{configs.n_m}_Error',
            'time': time.time() - start_time,
            'steps': 0,
            'problem_size': f"{configs.n_j}x{configs.n_m}",
            'operations': configs.n_op
        }

def run_super_enhanced_large_scale(job_length, op_pt, configs, model, device):
    """运行Super Enhanced DRL+分支定价算法（大规模优化版）"""
    print("    Running Super Enhanced DRL + Branch&Price (Large Scale)...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    # 根据问题规模调整参数
    if configs.n_op <= 200:  # 20x10规模
        configs.max_iterations = 5
        configs.cg_topk = 30
    elif configs.n_op <= 400:  # 30x10规模
        configs.max_iterations = 4
        configs.cg_topk = 40
    else:  # 40x10及更大规模
        configs.max_iterations = 3
        configs.cg_topk = 50
    
    configs.device = device
    
    try:
        # 确保模型在正确设备上
        model = model.to(device)
        model.eval()
        
        column_generator = SuperEnhancedColumnGenerator(model, configs)
        master_solver = EnhancedMasterSolver(configs)
        
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        iteration = 0
        total_columns = 0
        best_makespan = float('inf')
        best_schedule = None
        convergence_history = []
        
        while iteration < configs.max_iterations:
            iteration += 1
            iter_start_time = time.time()
            
            # 获取对偶价格
            dual_values = master_solver.get_dual_prices() if iteration > 1 else None
            
            # 使用超级增强的列生成
            new_columns = column_generator.generate_columns(
                env, dual_values=dual_values, top_k=configs.cg_topk
            )
            
            if not new_columns:
                print(f"      No new columns at iteration {iteration}")
                break
            
            total_columns += len(new_columns)
            master_solver.add_columns(new_columns)
            
            # 更新最佳解
            for col in new_columns:
                if col['makespan'] < best_makespan:
                    best_makespan = col['makespan']
                    best_schedule = col['schedule']
            
            # 求解主问题
            solution, min_reduced_cost = master_solver.solve()
            
            iter_time = time.time() - iter_start_time
            convergence_history.append({
                'iteration': iteration,
                'best_makespan': best_makespan,
                'min_reduced_cost': min_reduced_cost,
                'columns_added': len(new_columns),
                'iter_time': iter_time
            })
            
            print(f"      Iter {iteration}: best_makespan={best_makespan:.3f}, "
                  f"rc={min_reduced_cost:.5f}, cols={len(new_columns)}, time={iter_time:.1f}s")
            
            # 大规模问题的收敛判断（更宽松）
            if iteration > 1:
                prev_best = convergence_history[-2]['best_makespan']
                if prev_best > 0:
                    improvement = (prev_best - best_makespan) / prev_best
                    if improvement < 0.001:  # 0.1%改善阈值
                        print(f"      Converged by makespan improvement at iteration {iteration}")
                        break
            
            if min_reduced_cost >= -0.01:  # 收敛阈值
                print(f"      Converged by reduced cost at iteration {iteration}")
                break
        
        total_time = time.time() - start_time
        
        # 获取最终解
        if best_schedule is not None:
            result = {
                'makespan': best_makespan,
                'schedule': best_schedule,
                'method': f'Super_Enhanced_{configs.n_j}x{configs.n_m}_{iteration}iter',
                'time': total_time,
                'iterations': iteration,
                'total_columns': total_columns,
                'convergence_history': convergence_history,
                'problem_size': f"{configs.n_j}x{configs.n_m}",
                'operations': configs.n_op,
                'avg_iter_time': total_time / iteration if iteration > 0 else 0
            }
            
            print(f"      Super Enhanced: {best_makespan:.3f} (time: {total_time:.3f}s, "
                  f"iters: {iteration}, ops: {configs.n_op})")
            return result
        else:
            return {
                'makespan': float('inf'),
                'schedule': [],
                'method': f'Super_Enhanced_{configs.n_j}x{configs.n_m}_NoSolution',
                'time': total_time,
                'iterations': iteration,
                'total_columns': total_columns,
                'problem_size': f"{configs.n_j}x{configs.n_m}",
                'operations': configs.n_op
            }
            
    except Exception as e:
        print(f"      Super Enhanced failed: {e}")
        return {
            'makespan': float('inf'),
            'schedule': [],
            'method': f'Super_Enhanced_{configs.n_j}x{configs.n_m}_Error',
            'time': time.time() - start_time,
            'iterations': 0,
            'total_columns': 0,
            'problem_size': f"{configs.n_j}x{configs.n_m}",
            'operations': configs.n_op
        }

def test_single_instance_large_scale(instance_path, model, device):
    """在单个大规模实例上测试两种算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")
    
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    results = {}
    
    # 算法1: 纯DRL
    try:
        results['drl'] = run_pure_drl_large_scale(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        results['drl'] = {
            'makespan': float('inf'), 'time': 0, 'method': 'Pure_DRL_Failed',
            'problem_size': f"{n_j}x{n_m}", 'operations': n_op
        }
    
    # 算法2: Super Enhanced DRL+分支定价
    try:
        results['super_enhanced'] = run_super_enhanced_large_scale(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Super Enhanced failed: {e}")
        results['super_enhanced'] = {
            'makespan': float('inf'), 'time': 0, 'method': 'Super_Enhanced_Failed',
            'problem_size': f"{n_j}x{n_m}", 'operations': n_op
        }
    
    # 分析结果
    drl_makespan = results['drl']['makespan']
    super_makespan = results['super_enhanced']['makespan']
    drl_time = results['drl']['time']
    super_time = results['super_enhanced']['time']
    
    # 计算改善百分比
    improvement = 0
    if drl_makespan != float('inf') and super_makespan != float('inf'):
        improvement = (drl_makespan - super_makespan) / drl_makespan * 100
    
    # 找出最佳算法
    if drl_makespan != float('inf') and super_makespan != float('inf'):
        best_algorithm = 'super_enhanced' if super_makespan <= drl_makespan else 'drl'
    elif drl_makespan != float('inf'):
        best_algorithm = 'drl'
    elif super_makespan != float('inf'):
        best_algorithm = 'super_enhanced'
    else:
        best_algorithm = 'none'
    
    print(f"  Results:")
    print(f"    Pure DRL:                {drl_makespan:.3f} ({drl_time:.3f}s)")
    print(f"    Super Enhanced DRL+B&P:  {super_makespan:.3f} ({super_time:.3f}s)")
    print(f"    Best Algorithm:          {best_algorithm}")
    print(f"    Quality Improvement:     {improvement:.2f}%")
    print(f"    Time Ratio:              {super_time/drl_time:.1f}x" if drl_time > 0 else "    Time Ratio:              N/A")
    
    return {
        'instance': os.path.basename(instance_path),
        'problem_size': f"{n_j}x{n_m}",
        'n_jobs': n_j,
        'n_machines': n_m,
        'n_operations': n_op,
        'drl_makespan': drl_makespan,
        'super_makespan': super_makespan,
        'drl_time': drl_time,
        'super_time': super_time,
        'improvement': improvement,
        'time_ratio': super_time/drl_time if drl_time > 0 else float('inf'),
        'best_algorithm': best_algorithm,
        'drl_method': results['drl']['method'],
        'super_method': results['super_enhanced']['method'],
        'super_iterations': results['super_enhanced'].get('iterations', 0),
        'super_columns': results['super_enhanced'].get('total_columns', 0),
        'convergence_history': results['super_enhanced'].get('convergence_history', [])
    }

def run_large_scale_comparison():
    """运行大规模对比测试"""
    print("=== LARGE SCALE Super Enhanced DRL vs Pure DRL Comparison ===")
    print("Testing Scales: 30x10 (~300 ops) and 40x10 (~400 ops)")
    print("Goal: Validate scalability and superiority of Super Enhanced algorithm")
    print("="*80)

    # 测试配置
    test_configs = [
        {
            'name': '30x10',
            'data_path': './data/SD2/30x10+mix',
            'model_path': './trained_network/SD2/20x10+mix.pth',  # 使用20x10模型进行跨规模测试
            'max_instances': 8
        },
        {
            'name': '40x10',
            'data_path': './data/SD2/40x10+mix',
            'model_path': './trained_network/SD2/20x10+mix.pth',  # 使用20x10模型进行跨规模测试
            'max_instances': 6
        }
    ]

    all_results = []

    for config in test_configs:
        print(f"\n{'='*20} Testing {config['name']} Dataset {'='*20}")

        # 加载模型
        model = load_trained_model(config['model_path'], configs, device)
        if model is None:
            print(f"Failed to load model for {config['name']}, skipping...")
            continue

        # 检查数据路径
        if not os.path.exists(config['data_path']):
            print(f"Data path {config['data_path']} not found, skipping...")
            continue

        # 获取测试文件
        test_files = [f for f in os.listdir(config['data_path']) if f.endswith('.fjs')]
        test_files.sort()

        if not test_files:
            print(f"No .fjs files found in {config['data_path']}")
            continue

        # 限制测试数量
        test_files = test_files[:config['max_instances']]
        print(f"Testing {len(test_files)} instances from {config['name']} dataset...")

        scale_results = []

        for i, filename in enumerate(test_files):
            print(f"\n[{i+1}/{len(test_files)}] Processing {filename}...")
            instance_path = os.path.join(config['data_path'], filename)

            try:
                result = test_single_instance_large_scale(instance_path, model, device)
                result['dataset'] = config['name']
                scale_results.append(result)
                all_results.append(result)
            except Exception as e:
                print(f"Error processing {filename}: {e}")
                continue

        # 分析当前规模的结果
        if scale_results:
            analyze_scale_results(scale_results, config['name'])

    # 综合分析所有结果
    if all_results:
        comprehensive_analysis(all_results)
        save_large_scale_results(all_results)
        create_large_scale_visualization(all_results)

    return all_results

def analyze_scale_results(results, scale_name):
    """分析单个规模的结果"""
    valid_results = [r for r in results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]

    if not valid_results:
        print(f"\n❌ No valid results for {scale_name}")
        return

    # 计算统计数据
    avg_drl = np.mean([r['drl_makespan'] for r in valid_results])
    avg_super = np.mean([r['super_makespan'] for r in valid_results])
    avg_improvement = np.mean([r['improvement'] for r in valid_results])
    avg_drl_time = np.mean([r['drl_time'] for r in valid_results])
    avg_super_time = np.mean([r['super_time'] for r in valid_results])
    avg_time_ratio = np.mean([r['time_ratio'] for r in valid_results])
    avg_operations = np.mean([r['n_operations'] for r in valid_results])

    # 胜率统计
    wins_super = len([r for r in valid_results if r['best_algorithm'] == 'super_enhanced'])
    win_rate = wins_super / len(valid_results) * 100

    print(f"\n📊 {scale_name} Results Summary:")
    print(f"  Problem Scale:           {avg_operations:.0f} operations average")
    print(f"  Valid Results:           {len(valid_results)}/{len(results)}")
    print(f"  Super Enhanced Win Rate: {win_rate:.1f}% ({wins_super}/{len(valid_results)})")
    print(f"  Average Quality Improvement: {avg_improvement:.2f}%")
    print(f"  Average Time Ratio:      {avg_time_ratio:.1f}x")
    print(f"  DRL Average Time:        {avg_drl_time:.1f}s")
    print(f"  Super Enhanced Time:     {avg_super_time:.1f}s")

    # 质量分析
    improvements = [r['improvement'] for r in valid_results]
    positive_improvements = [imp for imp in improvements if imp > 0]
    significant_improvements = [imp for imp in improvements if imp > 2.0]

    print(f"  Best Improvement:        {max(improvements):.2f}%")
    print(f"  Positive Improvements:   {len(positive_improvements)}/{len(improvements)} ({len(positive_improvements)/len(improvements)*100:.1f}%)")
    print(f"  Significant (>2%):       {len(significant_improvements)}/{len(improvements)} ({len(significant_improvements)/len(improvements)*100:.1f}%)")

def comprehensive_analysis(all_results):
    """综合分析所有规模的结果"""
    print(f"\n{'='*20} COMPREHENSIVE ANALYSIS {'='*20}")

    # 按规模分组
    results_by_scale = {}
    for result in all_results:
        scale = result['dataset']
        if scale not in results_by_scale:
            results_by_scale[scale] = []
        results_by_scale[scale].append(result)

    print(f"\n🎯 SCALABILITY ANALYSIS:")

    for scale, results in results_by_scale.items():
        valid_results = [r for r in results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]
        if valid_results:
            avg_operations = np.mean([r['n_operations'] for r in valid_results])
            avg_improvement = np.mean([r['improvement'] for r in valid_results])
            win_rate = len([r for r in valid_results if r['best_algorithm'] == 'super_enhanced']) / len(valid_results) * 100
            avg_time_ratio = np.mean([r['time_ratio'] for r in valid_results])

            print(f"  {scale} ({avg_operations:.0f} ops): {win_rate:.1f}% win rate, {avg_improvement:.2f}% avg improvement, {avg_time_ratio:.1f}x time")

    # 总体统计
    all_valid = [r for r in all_results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]
    if all_valid:
        total_wins = len([r for r in all_valid if r['best_algorithm'] == 'super_enhanced'])
        total_win_rate = total_wins / len(all_valid) * 100
        total_avg_improvement = np.mean([r['improvement'] for r in all_valid])

        print(f"\n🏆 OVERALL PERFORMANCE:")
        print(f"  Total Instances:         {len(all_valid)}")
        print(f"  Overall Win Rate:        {total_win_rate:.1f}% ({total_wins}/{len(all_valid)})")
        print(f"  Overall Avg Improvement: {total_avg_improvement:.2f}%")

        # 成功评估
        if total_win_rate >= 70 and total_avg_improvement >= 1.5:
            print(f"  🎉🎉🎉 OUTSTANDING SUCCESS: Super Enhanced dominates at large scale!")
        elif total_win_rate >= 60 and total_avg_improvement >= 1.0:
            print(f"  🎉🎉 EXCELLENT SUCCESS: Super Enhanced clearly superior!")
        elif total_win_rate >= 50:
            print(f"  🎉 SUCCESS: Super Enhanced outperforms Pure DRL!")
        else:
            print(f"  ⚠️ MIXED RESULTS: Performance varies by scale")

def save_large_scale_results(results):
    """保存大规模测试结果"""
    if not results:
        return

    df = pd.DataFrame(results)

    if not os.path.exists('./comparison_results'):
        os.makedirs('./comparison_results')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/large_scale_super_enhanced_{timestamp}.csv"
    df.to_csv(filename, index=False)
    print(f"\n📁 Large scale results saved to: {filename}")
    return filename

def create_large_scale_visualization(results):
    """创建大规模测试的可视化图表"""
    if not results:
        return

    valid_results = [r for r in results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]
    if not valid_results:
        print("No valid results to visualize")
        return

    # 按数据集分组
    datasets = list(set(r['dataset'] for r in valid_results))

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))

    # 颜色配置
    colors = {'30x10': '#FF6B6B', '40x10': '#4ECDC4', 'drl': '#45B7D1', 'super': '#96CEB4'}

    # 1. 按规模的质量改善对比
    scale_improvements = {}
    scale_win_rates = {}

    for dataset in datasets:
        dataset_results = [r for r in valid_results if r['dataset'] == dataset]
        improvements = [r['improvement'] for r in dataset_results]
        wins = len([r for r in dataset_results if r['best_algorithm'] == 'super_enhanced'])

        scale_improvements[dataset] = np.mean(improvements)
        scale_win_rates[dataset] = wins / len(dataset_results) * 100

    scales = list(scale_improvements.keys())
    improvements = list(scale_improvements.values())
    win_rates = list(scale_win_rates.values())

    bars1 = ax1.bar(scales, improvements, color=[colors.get(s, '#CCCCCC') for s in scales], alpha=0.8)
    ax1.set_ylabel('Average Quality Improvement (%)')
    ax1.set_title('Quality Improvement by Problem Scale', fontweight='bold', fontsize=14)
    ax1.grid(True, alpha=0.3)

    # 添加数值标签
    for bar, imp in zip(bars1, improvements):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{imp:.2f}%', ha='center', va='bottom', fontweight='bold')

    # 2. 胜率对比
    bars2 = ax2.bar(scales, win_rates, color=[colors.get(s, '#CCCCCC') for s in scales], alpha=0.8)
    ax2.set_ylabel('Win Rate (%)')
    ax2.set_title('Super Enhanced Win Rate by Scale', fontweight='bold', fontsize=14)
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=50, color='red', linestyle='--', alpha=0.7, label='50% Baseline')
    ax2.legend()

    # 添加数值标签
    for bar, rate in zip(bars2, win_rates):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

    # 3. 时间效率分析
    scale_time_ratios = {}
    for dataset in datasets:
        dataset_results = [r for r in valid_results if r['dataset'] == dataset]
        time_ratios = [r['time_ratio'] for r in dataset_results if r['time_ratio'] != float('inf')]
        if time_ratios:
            scale_time_ratios[dataset] = np.mean(time_ratios)

    if scale_time_ratios:
        scales_time = list(scale_time_ratios.keys())
        ratios = list(scale_time_ratios.values())

        bars3 = ax3.bar(scales_time, ratios, color='#FFEAA7', alpha=0.8)
        ax3.set_ylabel('Time Ratio (Super Enhanced / Pure DRL)')
        ax3.set_title('Computational Efficiency by Scale', fontweight='bold', fontsize=14)
        ax3.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, ratio in zip(bars3, ratios):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{ratio:.1f}x', ha='center', va='bottom', fontweight='bold')

    # 4. 问题规模 vs 性能散点图
    operations = [r['n_operations'] for r in valid_results]
    improvements_all = [r['improvement'] for r in valid_results]
    datasets_all = [r['dataset'] for r in valid_results]

    for dataset in datasets:
        dataset_ops = [r['n_operations'] for r in valid_results if r['dataset'] == dataset]
        dataset_imps = [r['improvement'] for r in valid_results if r['dataset'] == dataset]
        ax4.scatter(dataset_ops, dataset_imps, label=dataset,
                   color=colors.get(dataset, '#CCCCCC'), alpha=0.7, s=60)

    ax4.set_xlabel('Number of Operations')
    ax4.set_ylabel('Quality Improvement (%)')
    ax4.set_title('Scalability: Operations vs Quality Improvement', fontweight='bold', fontsize=14)
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)

    plt.suptitle('Large Scale Super Enhanced DRL+B&P vs Pure DRL\nScalability and Performance Analysis',
                fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/large_scale_visualization_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📊 Large scale visualization saved: {filename}")

    plt.show()
    return fig

if __name__ == "__main__":
    results = run_large_scale_comparison()

    if results:
        print(f"\n=== LARGE SCALE TEST COMPLETED ===")
        print(f"Total results: {len(results)}")
        print("Check ./comparison_results/ for detailed results and visualizations")

        # 最终评估
        valid_results = [r for r in results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]
        if valid_results:
            total_wins = len([r for r in valid_results if r['best_algorithm'] == 'super_enhanced'])
            success_rate = total_wins / len(valid_results) * 100
            avg_improvement = np.mean([r['improvement'] for r in valid_results])

            print(f"\n🎯 FINAL LARGE SCALE ASSESSMENT:")
            print(f"   Success Rate: {success_rate:.1f}%")
            print(f"   Average Improvement: {avg_improvement:.2f}%")
            print(f"   Tested Scales: 30x10 (~300 ops), 40x10 (~400 ops)")

            if success_rate >= 70:
                print("   🎉🎉🎉 EXCEPTIONAL: Super Enhanced dominates at large scale!")
            elif success_rate >= 60:
                print("   🎉🎉 EXCELLENT: Super Enhanced clearly superior at scale!")
            elif success_rate >= 50:
                print("   🎉 SUCCESS: Super Enhanced maintains advantage at scale!")
            else:
                print("   ⚠️ CHALLENGE: Large scale poses difficulties, needs optimization")
    else:
        print("❌ No results generated")
