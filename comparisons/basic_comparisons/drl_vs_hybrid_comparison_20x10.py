#!/usr/bin/env python3
"""
DRL vs DRL+分支定价对比测试 - SD2 20x10+MIX数据集
专门对比纯DRL和DRL+分支定价混合算法的性能
"""

import time
import torch
import numpy as np
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from improved_master_solver import ImprovedMasterSolver
from improved_column_generator import ImprovedColumnGenerator
from params import configs
from data_utils import text_to_matrix
from common_utils import heuristic_select_action
import copy

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
sns.set_style("whitegrid")

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

def load_trained_model(model_path, configs, device):
    """加载训练好的DRL模型"""
    model = DANIEL(configs)
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"✓ Loaded trained model: {model_path}")
    else:
        print(f"✗ Model not found: {model_path}")
        return None
    model.to(device)
    model.eval()
    print(f"✓ Model moved to {device}")
    return model

def run_pure_drl_with_model(job_length, op_pt, configs, model, device):
    """使用DRL模型进行推理的纯DRL算法"""
    print("    Running Pure DRL with Model Inference...")
    start_time = time.time()

    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]

    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])

    schedule = []
    step = 0
    done = False
    max_steps = 3000

    with torch.no_grad():
        while not done and step < max_steps:
            # 获取环境状态
            state = env.state

            # 将状态张量移到GPU
            fea_j = state.fea_j_tensor.to(device)
            op_mask = state.op_mask_tensor.to(device)
            candidate = state.candidate_tensor.to(device)
            fea_m = state.fea_m_tensor.to(device)
            mch_mask = state.mch_mask_tensor.to(device)
            comp_idx = state.comp_idx_tensor.to(device)
            dynamic_pair_mask = state.dynamic_pair_mask_tensor.to(device)
            fea_pairs = state.fea_pairs_tensor.to(device)

            # 模型推理
            pi, _ = model(fea_j, op_mask, candidate, fea_m,
                         mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)

            # 选择最佳动作
            action = torch.argmax(pi, dim=1).cpu().numpy()[0]

            # 记录调度信息
            chosen_job = action // env.number_of_machines
            chosen_mch = action % env.number_of_machines
            chosen_op = env.candidate[0, chosen_job]

            start_op_time = max(env.candidate_free_time[0, chosen_job],
                               env.mch_free_time[0, chosen_mch])
            processing_time = env.true_op_pt[0, chosen_op, chosen_mch]
            end_op_time = start_op_time + processing_time

            schedule.append({
                "op_id": chosen_op,
                "job_id": chosen_job,
                "mch_id": chosen_mch,
                "start_time": start_op_time,
                "end_time": end_op_time,
                "processing_time": processing_time
            })

            # 执行动作
            state, reward, done_array = env.step(np.array([action]))
            done = done_array[0]
            step += 1

    makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
    total_time = time.time() - start_time

    result = {
        'makespan': makespan,
        'schedule': schedule,
        'method': 'Pure_DRL_Model',
        'time': total_time
    }

    print(f"      Pure DRL (Model): {makespan:.3f} (time: {total_time:.3f}s, steps: {step})")
    return result

def run_pure_drl(job_length, op_pt, configs, model, device):
    """运行纯DRL算法（启发式方法作为对比）"""
    print("    Running Pure DRL...")
    start_time = time.time()

    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]

    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])

    # 首先尝试使用DRL模型推理
    try:
        model_result = run_pure_drl_with_model(job_length, op_pt, configs, model, device)
        return model_result
    except Exception as e:
        print(f"      DRL model inference failed: {e}, falling back to heuristics")

    # 如果模型推理失败，使用启发式方法
    heuristic_methods = ['SPT', 'FIFO', 'MOR', 'MWKR']
    best_result = None
    best_makespan = float('inf')
    
    for method in heuristic_methods:
        test_env = copy.deepcopy(env)
        schedule = []
        
        step = 0
        done = False
        max_steps = 3000  # 增加最大步数以适应20x10规模
        
        while not done and step < max_steps:
            action = heuristic_select_action(method, test_env)
            
            chosen_job = action // test_env.number_of_machines
            chosen_mch = action % test_env.number_of_machines
            chosen_op = test_env.candidate[0, chosen_job]
            
            start_op_time = max(test_env.candidate_free_time[0, chosen_job], 
                               test_env.mch_free_time[0, chosen_mch])
            processing_time = test_env.true_op_pt[0, chosen_op, chosen_mch]
            end_op_time = start_op_time + processing_time
            
            schedule.append({
                "op_id": chosen_op,
                "job_id": chosen_job,
                "mch_id": chosen_mch,
                "start_time": start_op_time,
                "end_time": end_op_time,
                "processing_time": processing_time
            })
            
            state, reward, done_array = test_env.step(np.array([action]))
            done = done_array[0]
            step += 1
        
        makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
        
        if makespan < best_makespan:
            best_makespan = makespan
            best_result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': f'Pure_DRL_{method}',
                'heuristic_used': method
            }
    
    total_time = time.time() - start_time
    best_result['time'] = total_time
    
    print(f"      Pure DRL: {best_result['makespan']:.3f} (time: {total_time:.3f}s, method: {best_result['heuristic_used']})")
    return best_result

def run_drl_branch_price(job_length, op_pt, configs, model, device):
    """运行DRL+分支定价混合算法"""
    print("    Running DRL + Branch&Price...")
    start_time = time.time()

    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    configs.max_iterations = 15  # 减少迭代次数以加快速度
    configs.cg_topk = 6  # 减少每次生成的列数
    configs.device = device  # 确保配置中有device信息

    column_generator = ImprovedColumnGenerator(model, configs)
    master_solver = ImprovedMasterSolver(configs)
    
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    iteration = 0
    total_columns = 0
    
    while iteration < configs.max_iterations:
        iteration += 1
        
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
        
        if not new_columns:
            print(f"      No new columns generated at iteration {iteration}")
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        solution, reduced_cost = master_solver.solve()
        
        if reduced_cost >= -1e-4:
            print(f"      Optimal solution found at iteration {iteration}")
            break
    
    total_time = time.time() - start_time
    
    best_makespan = float('inf')
    best_schedule = None
    
    if solution:
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
    
    result = {
        'makespan': best_makespan,
        'time': total_time,
        'schedule': best_schedule,
        'iterations': iteration,
        'total_columns': total_columns,
        'method': 'DRL_BranchPrice'
    }
    
    print(f"      DRL+B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {iteration}, cols: {total_columns})")
    return result

def test_single_instance(instance_path, model, device):
    """在单个实例上测试两种算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")

    with open(instance_path, 'r') as f:
        lines = f.readlines()

    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]

    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")

    results = {}

    # 算法1: 纯DRL
    try:
        results['drl'] = run_pure_drl(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        results['drl'] = {'makespan': float('inf'), 'time': 0, 'method': 'Pure_DRL_Failed'}

    # 算法2: DRL+分支定价
    try:
        results['hybrid'] = run_drl_branch_price(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      DRL+B&P failed: {e}")
        results['hybrid'] = {'makespan': float('inf'), 'time': 0, 'method': 'DRL_BranchPrice_Failed'}
    
    # 分析结果
    drl_makespan = results['drl']['makespan']
    hybrid_makespan = results['hybrid']['makespan']
    drl_time = results['drl']['time']
    hybrid_time = results['hybrid']['time']
    
    improvement = 0
    if drl_makespan != float('inf') and hybrid_makespan != float('inf'):
        improvement = (drl_makespan - hybrid_makespan) / drl_makespan * 100
    
    print(f"  Results:")
    print(f"    Pure DRL:        {drl_makespan:.3f} ({drl_time:.3f}s)")
    print(f"    DRL+B&P:         {hybrid_makespan:.3f} ({hybrid_time:.3f}s)")
    print(f"    Improvement:     {improvement:.2f}%")
    print(f"    Time ratio:      {hybrid_time/drl_time:.1f}x" if drl_time > 0 else "    Time ratio:      N/A")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'drl_makespan': drl_makespan,
        'hybrid_makespan': hybrid_makespan,
        'drl_time': drl_time,
        'hybrid_time': hybrid_time,
        'improvement': improvement,
        'time_ratio': hybrid_time/drl_time if drl_time > 0 else float('inf'),
        'better_algorithm': 'DRL+B&P' if hybrid_makespan < drl_makespan else 'Pure DRL'
    }

def create_comparison_visualization(results):
    """创建对比可视化图表"""
    if not results:
        print("No results to visualize")
        return

    # 准备数据
    instances = [r['instance'] for r in results]
    drl_makespans = [r['drl_makespan'] for r in results if r['drl_makespan'] != float('inf')]
    hybrid_makespans = [r['hybrid_makespan'] for r in results if r['hybrid_makespan'] != float('inf')]
    improvements = [r['improvement'] for r in results if r['improvement'] != 0]
    time_ratios = [r['time_ratio'] for r in results if r['time_ratio'] != float('inf')]

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 颜色配置
    colors = {
        'drl': '#FF6B6B',
        'hybrid': '#4ECDC4',
        'improvement': '#45B7D1'
    }

    # 1. Makespan对比
    if len(drl_makespans) == len(hybrid_makespans):
        x = np.arange(len(drl_makespans))
        width = 0.35

        bars1 = ax1.bar(x - width/2, drl_makespans, width,
                       label='Pure DRL', color=colors['drl'], alpha=0.8)
        bars2 = ax1.bar(x + width/2, hybrid_makespans, width,
                       label='DRL + Branch&Price', color=colors['hybrid'], alpha=0.8)

        ax1.set_xlabel('Test Instance')
        ax1.set_ylabel('Makespan')
        ax1.set_title('Makespan Comparison: Pure DRL vs DRL+Branch&Price (20x10+MIX)', fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels([f"#{i+1}" for i in range(len(drl_makespans))])
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # 2. 改进百分比
    if improvements:
        colors_bar = ['green' if imp > 0 else 'red' for imp in improvements]
        bars3 = ax2.bar(range(len(improvements)), improvements, color=colors_bar, alpha=0.7)
        ax2.set_xlabel('Test Instance')
        ax2.set_ylabel('Makespan Improvement (%)')
        ax2.set_title('Makespan Improvement by Instance', fontweight='bold')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for i, v in enumerate(improvements):
            ax2.text(i, v + (max(improvements) * 0.02), f'{v:.1f}%',
                    ha='center', va='bottom' if v >= 0 else 'top', fontweight='bold')

    # 3. 时间对比
    if time_ratios:
        ax3.bar(range(len(time_ratios)), time_ratios, color=colors['improvement'], alpha=0.7)
        ax3.set_xlabel('Test Instance')
        ax3.set_ylabel('Time Ratio (Hybrid/DRL)')
        ax3.set_title('Computation Time Ratio', fontweight='bold')
        ax3.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='Equal Time')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. 总体统计
    if improvements:
        avg_improvement = np.mean(improvements)
        best_improvement = max(improvements)
        success_rate = sum(1 for x in improvements if x > 0) / len(improvements) * 100

        stats = ['Avg Improvement', 'Best Improvement', 'Success Rate']
        values = [avg_improvement, best_improvement, success_rate]

        bars4 = ax4.bar(stats, values, color=[colors['improvement']] * 3, alpha=0.8)
        ax4.set_ylabel('Percentage (%)')
        ax4.set_title('Overall Performance Statistics', fontweight='bold')
        ax4.grid(True, alpha=0.3)

        # 添加数值标签
        for i, v in enumerate(values):
            if i == 2:  # Success rate
                ax4.text(i, v + 1, f'{v:.0f}%', ha='center', va='bottom', fontweight='bold')
            else:
                ax4.text(i, v + 0.1, f'{v:.1f}%', ha='center', va='bottom', fontweight='bold')

    plt.suptitle('DRL vs DRL+Branch&Price Comparison on SD2 20x10+MIX Dataset',
                fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    if not os.path.exists('./comparison_results'):
        os.makedirs('./comparison_results')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/drl_vs_hybrid_20x10_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"Comparison visualization saved: {filename}")

    plt.show()
    return fig

def save_results_to_csv(results):
    """保存结果到CSV文件"""
    if not results:
        return

    df = pd.DataFrame(results)

    if not os.path.exists('./comparison_results'):
        os.makedirs('./comparison_results')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/drl_vs_hybrid_20x10_{timestamp}.csv"
    df.to_csv(filename, index=False)
    print(f"Results saved to CSV: {filename}")

    return filename

def run_drl_vs_hybrid_comparison():
    """运行DRL vs DRL+分支定价的完整对比测试"""
    print("=== DRL vs DRL+Branch&Price Comparison on SD2 20x10+MIX ===")
    print("Dataset: SD2 20x10+MIX")
    print("Algorithms: Pure DRL vs DRL+Branch&Price")
    print("="*70)

    # 加载训练好的模型
    model_path = './trained_network/SD2/20x10+mix.pth'
    model = load_trained_model(model_path, configs, device)

    if model is None:
        print("Failed to load model, exiting...")
        return []

    # 测试数据路径
    data_path = './data/SD2/20x10+mix'

    if not os.path.exists(data_path):
        print(f"Data path {data_path} not found!")
        return []

    # 获取测试实例
    instance_files = [f for f in os.listdir(data_path) if f.endswith('.fjs')]
    instance_files.sort()

    # 选择测试实例数量（可以调整）
    test_count = 5  # 减少到5个实例以加快速度
    test_instances = instance_files[:test_count]

    print(f"Testing {len(test_instances)} instances from {data_path}")

    all_results = []

    for i, instance_file in enumerate(test_instances):
        print(f"\n=== Test {i+1}/{len(test_instances)} ===")
        instance_path = os.path.join(data_path, instance_file)

        try:
            result = test_single_instance(instance_path, model, device)
            all_results.append(result)

            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

        except Exception as e:
            print(f"  Error testing {instance_file}: {e}")
            continue

    # 汇总分析
    if all_results:
        print(f"\n" + "="*80)
        print(f"=== COMPARISON SUMMARY ===")
        print(f"="*80)

        valid_results = [r for r in all_results if r['drl_makespan'] != float('inf') and r['hybrid_makespan'] != float('inf')]

        if valid_results:
            improvements = [r['improvement'] for r in valid_results]
            time_ratios = [r['time_ratio'] for r in valid_results]

            avg_improvement = np.mean(improvements)
            best_improvement = max(improvements)
            worst_improvement = min(improvements)
            success_count = sum(1 for x in improvements if x > 0)
            success_rate = success_count / len(improvements) * 100

            avg_time_ratio = np.mean(time_ratios)

            print(f"\nPerformance Summary:")
            print(f"  Average makespan improvement: {avg_improvement:.2f}%")
            print(f"  Best improvement: {best_improvement:.2f}%")
            print(f"  Worst improvement: {worst_improvement:.2f}%")
            print(f"  Success rate: {success_count}/{len(improvements)} ({success_rate:.1f}%)")
            print(f"  Average time ratio: {avg_time_ratio:.1f}x")

            # 详细结果表
            print(f"\nDetailed Results:")
            print(f"{'Instance':<20} {'DRL':<10} {'Hybrid':<10} {'Improve%':<10} {'TimeRatio':<10} {'Winner':<12}")
            print("-" * 75)
            for r in valid_results:
                print(f"{r['instance'][:19]:<20} {r['drl_makespan']:<10.2f} {r['hybrid_makespan']:<10.2f} "
                      f"{r['improvement']:<10.2f} {r['time_ratio']:<10.1f} {r['better_algorithm']:<12}")

        # 保存结果和创建可视化
        save_results_to_csv(all_results)
        create_comparison_visualization(valid_results)

    return all_results

if __name__ == "__main__":
    results = run_drl_vs_hybrid_comparison()

    if results:
        print(f"\n✓ DRL vs DRL+Branch&Price comparison completed successfully!")
        print(f"  Tested {len(results)} instances on SD2 20x10+MIX dataset")
        print(f"  Results and visualizations saved in './comparison_results/' folder")
    else:
        print(f"\n✗ No successful tests!")
