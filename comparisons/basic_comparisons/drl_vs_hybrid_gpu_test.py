#!/usr/bin/env python3
"""
DRL vs DRL+分支定价对比测试 - GPU加速版本
专门针对SD2 20x10+MIX数据集的快速测试
"""

import time
import torch
import numpy as np
import os
import pandas as pd
from datetime import datetime
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from params import configs
from data_utils import text_to_matrix
from common_utils import heuristic_select_action
import copy

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

def load_trained_model(model_path, configs, device):
    """加载训练好的DRL模型到GPU"""
    model = DANIEL(configs)
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location=device))
        print(f"✓ Loaded trained model: {model_path}")
    else:
        print(f"✗ Model not found: {model_path}")
        return None
    model.to(device)
    model.eval()
    print(f"✓ Model moved to {device}")
    return model

def run_drl_gpu(job_length, op_pt, configs, model, device):
    """在GPU上运行DRL推理"""
    print("    Running DRL on GPU...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    schedule = []
    step = 0
    done = False
    max_steps = 2000  # 减少最大步数以加快速度
    
    with torch.no_grad():
        while not done and step < max_steps:
            try:
                # 获取环境状态
                state = env.state
                
                # 将所有张量移到GPU
                fea_j = state.fea_j_tensor.to(device)
                op_mask = state.op_mask_tensor.to(device)
                candidate = state.candidate_tensor.to(device)
                fea_m = state.fea_m_tensor.to(device)
                mch_mask = state.mch_mask_tensor.to(device)
                comp_idx = state.comp_idx_tensor.to(device)
                dynamic_pair_mask = state.dynamic_pair_mask_tensor.to(device)
                fea_pairs = state.fea_pairs_tensor.to(device)
                
                # GPU上的模型推理
                pi, _ = model(fea_j, op_mask, candidate, fea_m, 
                             mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 选择最佳动作（移回CPU）
                action = torch.argmax(pi, dim=1).cpu().numpy()[0]
                
                # 记录调度信息
                chosen_job = action // env.number_of_machines
                chosen_mch = action % env.number_of_machines
                chosen_op = env.candidate[0, chosen_job]
                
                start_op_time = max(env.candidate_free_time[0, chosen_job], 
                                   env.mch_free_time[0, chosen_mch])
                processing_time = env.true_op_pt[0, chosen_op, chosen_mch]
                end_op_time = start_op_time + processing_time
                
                schedule.append({
                    "op_id": chosen_op,
                    "job_id": chosen_job,
                    "mch_id": chosen_mch,
                    "start_time": start_op_time,
                    "end_time": end_op_time,
                    "processing_time": processing_time
                })
                
                # 执行动作
                state, reward, done_array = env.step(np.array([action]))
                done = done_array[0]
                step += 1
                
            except Exception as e:
                print(f"      Error in DRL step {step}: {e}")
                # 如果GPU推理失败，回退到启发式方法
                action = heuristic_select_action('SPT', env)
                state, reward, done_array = env.step(np.array([action]))
                done = done_array[0]
                step += 1
    
    makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
    total_time = time.time() - start_time
    
    result = {
        'makespan': makespan,
        'schedule': schedule,
        'method': 'DRL_GPU',
        'time': total_time,
        'steps': step
    }
    
    print(f"      DRL GPU: {makespan:.3f} (time: {total_time:.3f}s, steps: {step})")
    return result

def run_drl_heuristic(job_length, op_pt, configs):
    """运行启发式DRL（作为对比基准）"""
    print("    Running DRL Heuristic...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    # 使用最佳启发式方法
    best_methods = ['SPT', 'MWKR']
    best_result = None
    best_makespan = float('inf')
    
    for method in best_methods:
        test_env = copy.deepcopy(env)
        schedule = []
        
        step = 0
        done = False
        max_steps = 2000
        
        while not done and step < max_steps:
            action = heuristic_select_action(method, test_env)
            
            chosen_job = action // test_env.number_of_machines
            chosen_mch = action % test_env.number_of_machines
            chosen_op = test_env.candidate[0, chosen_job]
            
            start_op_time = max(test_env.candidate_free_time[0, chosen_job], 
                               test_env.mch_free_time[0, chosen_mch])
            processing_time = test_env.true_op_pt[0, chosen_op, chosen_mch]
            end_op_time = start_op_time + processing_time
            
            schedule.append({
                "op_id": chosen_op,
                "job_id": chosen_job,
                "mch_id": chosen_mch,
                "start_time": start_op_time,
                "end_time": end_op_time,
                "processing_time": processing_time
            })
            
            state, reward, done_array = test_env.step(np.array([action]))
            done = done_array[0]
            step += 1
        
        makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
        
        if makespan < best_makespan:
            best_makespan = makespan
            best_result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': f'DRL_Heuristic_{method}',
                'heuristic_used': method,
                'steps': step
            }
    
    total_time = time.time() - start_time
    best_result['time'] = total_time
    
    print(f"      DRL Heuristic: {best_result['makespan']:.3f} (time: {total_time:.3f}s, method: {best_result['heuristic_used']})")
    return best_result

def run_hybrid_gpu(job_length, op_pt, configs, model, device):
    """运行DRL+分支定价混合算法（GPU加速）"""
    print("    Running DRL + Branch&Price on GPU...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    configs.max_iterations = 10  # 减少迭代次数
    configs.cg_topk = 5  # 减少列数
    configs.device = device
    
    try:
        column_generator = ColumnGenerator(model, configs)
        master_solver = MasterSolver(configs)
        
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        iteration = 0
        total_columns = 0
        
        while iteration < configs.max_iterations:
            iteration += 1
            
            dual_values = master_solver.get_dual_prices() if iteration > 1 else None
            new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
            
            if not new_columns:
                print(f"      No new columns at iteration {iteration}")
                break
                
            total_columns += len(new_columns)
            master_solver.add_columns(new_columns)
            
            solution, reduced_cost = master_solver.solve()
            
            if reduced_cost >= -1e-4:
                print(f"      Converged at iteration {iteration}")
                break
        
        best_makespan = float('inf')
        best_schedule = None
        
        if solution:
            for col in solution:
                if 'makespan' in col and col['makespan'] < best_makespan:
                    best_makespan = col['makespan']
                    best_schedule = col['schedule']
        
    except Exception as e:
        print(f"      Hybrid algorithm failed: {e}")
        best_makespan = float('inf')
        best_schedule = None
        iteration = 0
        total_columns = 0
    
    total_time = time.time() - start_time
    
    result = {
        'makespan': best_makespan,
        'time': total_time,
        'schedule': best_schedule,
        'iterations': iteration,
        'total_columns': total_columns,
        'method': 'DRL_BranchPrice_GPU'
    }
    
    print(f"      Hybrid GPU: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {iteration})")
    return result

def test_single_instance_gpu(instance_path, model, device):
    """在单个实例上测试GPU加速算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")
    
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    results = {}
    
    # 算法1: DRL GPU推理
    try:
        results['drl_gpu'] = run_drl_gpu(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      DRL GPU failed: {e}")
        results['drl_gpu'] = {'makespan': float('inf'), 'time': 0, 'method': 'DRL_GPU_Failed'}
    
    # 算法2: DRL启发式（对比基准）
    try:
        results['drl_heuristic'] = run_drl_heuristic(job_length, op_pt, configs)
    except Exception as e:
        print(f"      DRL Heuristic failed: {e}")
        results['drl_heuristic'] = {'makespan': float('inf'), 'time': 0, 'method': 'DRL_Heuristic_Failed'}
    
    # 算法3: DRL+分支定价 GPU
    try:
        results['hybrid_gpu'] = run_hybrid_gpu(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Hybrid GPU failed: {e}")
        results['hybrid_gpu'] = {'makespan': float('inf'), 'time': 0, 'method': 'Hybrid_GPU_Failed'}
    
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 分析结果
    gpu_makespan = results['drl_gpu']['makespan']
    heuristic_makespan = results['drl_heuristic']['makespan']
    hybrid_makespan = results['hybrid_gpu']['makespan']
    
    print(f"  Results:")
    print(f"    DRL GPU:         {gpu_makespan:.3f} ({results['drl_gpu']['time']:.3f}s)")
    print(f"    DRL Heuristic:   {heuristic_makespan:.3f} ({results['drl_heuristic']['time']:.3f}s)")
    print(f"    Hybrid GPU:      {hybrid_makespan:.3f} ({results['hybrid_gpu']['time']:.3f}s)")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'results': results
    }

def main():
    """主函数"""
    print("=== DRL vs DRL+Branch&Price GPU Comparison ===")
    print("Dataset: SD2 20x10+MIX")
    print("="*50)
    
    # 加载模型
    model_path = './trained_network/SD2/20x10+mix.pth'
    model = load_trained_model(model_path, configs, device)
    
    if model is None:
        print("Failed to load model!")
        return
    
    # 测试数据
    data_path = './data/SD2/20x10+mix'
    instance_files = [f for f in os.listdir(data_path) if f.endswith('.fjs')]
    instance_files.sort()
    
    # 测试前3个实例
    test_instances = instance_files[:3]
    
    print(f"Testing {len(test_instances)} instances...")
    
    all_results = []
    for i, instance_file in enumerate(test_instances):
        print(f"\n=== Test {i+1}/{len(test_instances)} ===")
        instance_path = os.path.join(data_path, instance_file)
        
        try:
            result = test_single_instance_gpu(instance_path, model, device)
            all_results.append(result)
        except Exception as e:
            print(f"Error: {e}")
    
    print(f"\n✓ GPU comparison completed!")
    print(f"Tested {len(all_results)} instances")

if __name__ == "__main__":
    main()
