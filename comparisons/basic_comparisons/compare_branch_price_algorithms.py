#!/usr/bin/env python3
"""
对比两种分支定价算法：传统分支定价 vs 精确分支定价
在同一实例上测试性能差异
"""

import time
import numpy as np
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from traditional_branch_price import TraditionalBranchPrice
from exact_branch_and_price import ExactBranchAndPrice
from params import configs
from data_utils import text_to_matrix

# 设置matplotlib样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (14, 10)
sns.set_style("whitegrid")

def run_traditional_branch_price(job_length, op_pt, configs, time_limit=60):
    """运行传统分支定价算法"""
    print("    Running Traditional Branch&Price...")
    start_time = time.time()
    
    # 设置问题规模
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    try:
        solver = TraditionalBranchPrice(configs)
        result = solver.solve(job_length, op_pt, max_iterations=20, max_columns_per_iter=10)
        
        total_time = time.time() - start_time
        
        # 检查是否超时
        if total_time > time_limit:
            result['status'] = 'timeout'
        else:
            result['status'] = 'completed'
            
        result['time'] = total_time
        result['algorithm'] = 'Traditional'
        result['optimality_guaranteed'] = False
        
        print(f"      Traditional: {result.get('makespan', 'N/A'):.3f} "
              f"(time: {total_time:.3f}s, iters: {result.get('iterations', 0)}, "
              f"cols: {result.get('total_columns', 0)})")
        
        return result
        
    except Exception as e:
        print(f"      Traditional B&P failed: {e}")
        return {
            'makespan': float('inf'),
            'time': time.time() - start_time,
            'status': 'failed',
            'algorithm': 'Traditional',
            'error': str(e),
            'optimality_guaranteed': False
        }

def run_exact_branch_price(job_length, op_pt, configs, time_limit=120):
    """运行精确分支定价算法"""
    print("    Running Exact Branch&Price...")
    start_time = time.time()
    
    # 设置问题规模
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    try:
        solver = ExactBranchAndPrice(configs)
        result = solver.solve(job_length, op_pt, time_limit=time_limit)
        
        total_time = time.time() - start_time
        result['time'] = total_time
        result['algorithm'] = 'Exact'

        # 确保有status字段
        if 'status' not in result:
            if result.get('optimality_proven', False):
                result['status'] = 'optimal'
            elif total_time >= time_limit:
                result['status'] = 'timeout'
            else:
                result['status'] = 'completed'

        print(f"      Exact: {result.get('makespan', 'N/A'):.3f} "
              f"(time: {total_time:.3f}s, nodes: {result.get('nodes_processed', 0)}, "
              f"status: {result.get('status', 'unknown')})")

        return result
        
    except Exception as e:
        print(f"      Exact B&P failed: {e}")
        return {
            'makespan': float('inf'),
            'time': time.time() - start_time,
            'status': 'failed',
            'algorithm': 'Exact',
            'error': str(e),
            'optimality_guaranteed': False
        }

def test_single_instance(instance_path):
    """在单个实例上测试两种分支定价算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")
    
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    results = {}
    
    # 算法1: 传统分支定价
    results['traditional'] = run_traditional_branch_price(job_length, op_pt, configs)
    
    # 算法2: 精确分支定价
    results['exact'] = run_exact_branch_price(job_length, op_pt, configs)
    
    # 分析结果
    trad_makespan = results['traditional']['makespan']
    exact_makespan = results['exact']['makespan']
    trad_time = results['traditional']['time']
    exact_time = results['exact']['time']
    
    # 计算改进
    improvement = 0
    if trad_makespan != float('inf') and exact_makespan != float('inf'):
        improvement = (trad_makespan - exact_makespan) / trad_makespan * 100
    
    # 确定最佳算法
    if exact_makespan < trad_makespan:
        better_algorithm = 'Exact'
    elif trad_makespan < exact_makespan:
        better_algorithm = 'Traditional'
    else:
        better_algorithm = 'Tie'
    
    print(f"  Results Summary:")
    print(f"    Traditional B&P: {trad_makespan:.3f} ({trad_time:.3f}s, {results['traditional']['status']})")
    print(f"    Exact B&P:       {exact_makespan:.3f} ({exact_time:.3f}s, {results['exact']['status']})")
    print(f"    Improvement:     {improvement:.2f}% (Exact vs Traditional)")
    print(f"    Time ratio:      {exact_time/trad_time:.1f}x" if trad_time > 0 else "    Time ratio:      N/A")
    print(f"    Better algorithm: {better_algorithm}")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'traditional_makespan': trad_makespan,
        'exact_makespan': exact_makespan,
        'traditional_time': trad_time,
        'exact_time': exact_time,
        'traditional_status': results['traditional']['status'],
        'exact_status': results['exact']['status'],
        'improvement': improvement,
        'time_ratio': exact_time/trad_time if trad_time > 0 else float('inf'),
        'better_algorithm': better_algorithm,
        'traditional_optimality': results['traditional'].get('optimality_guaranteed', False),
        'exact_optimality': results['exact'].get('optimality_guaranteed', False)
    }

def create_comparison_visualization(results):
    """创建对比可视化图表"""
    if not results:
        print("No results to visualize")
        return
    
    # 准备数据
    valid_results = [r for r in results if r['traditional_makespan'] != float('inf') and r['exact_makespan'] != float('inf')]
    
    if not valid_results:
        print("No valid results for visualization")
        return
    
    instances = [r['instance'] for r in valid_results]
    trad_makespans = [r['traditional_makespan'] for r in valid_results]
    exact_makespans = [r['exact_makespan'] for r in valid_results]
    improvements = [r['improvement'] for r in valid_results]
    time_ratios = [r['time_ratio'] for r in valid_results if r['time_ratio'] != float('inf')]
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 颜色配置
    colors = {
        'traditional': '#FF6B6B',
        'exact': '#4ECDC4',
        'improvement': '#45B7D1'
    }
    
    # 1. Makespan对比
    x = np.arange(len(valid_results))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, trad_makespans, width, 
                   label='Traditional B&P', color=colors['traditional'], alpha=0.8)
    bars2 = ax1.bar(x + width/2, exact_makespans, width,
                   label='Exact B&P', color=colors['exact'], alpha=0.8)
    
    ax1.set_xlabel('Test Instance')
    ax1.set_ylabel('Makespan')
    ax1.set_title('Makespan Comparison: Traditional vs Exact Branch&Price', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f"#{i+1}" for i in range(len(valid_results))])
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 改进百分比
    colors_bar = ['green' if imp > 0 else 'red' for imp in improvements]
    bars3 = ax2.bar(range(len(improvements)), improvements, color=colors_bar, alpha=0.7)
    ax2.set_xlabel('Test Instance')
    ax2.set_ylabel('Makespan Improvement (%)')
    ax2.set_title('Quality Improvement (Exact vs Traditional)', fontweight='bold')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(improvements):
        ax2.text(i, v + (max(improvements) * 0.02), f'{v:.1f}%', 
                ha='center', va='bottom' if v >= 0 else 'top', fontweight='bold')
    
    # 3. 时间对比
    if time_ratios:
        ax3.bar(range(len(time_ratios)), time_ratios, color=colors['improvement'], alpha=0.7)
        ax3.set_xlabel('Test Instance')
        ax3.set_ylabel('Time Ratio (Exact/Traditional)')
        ax3.set_title('Computation Time Ratio', fontweight='bold')
        ax3.axhline(y=1, color='red', linestyle='--', alpha=0.5, label='Equal Time')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # 4. 算法成功率统计
    traditional_success = sum(1 for r in results if r['traditional_status'] == 'completed')
    exact_success = sum(1 for r in results if r['exact_status'] == 'completed')
    exact_optimal = sum(1 for r in results if r['exact_optimality'])
    
    categories = ['Traditional\nCompleted', 'Exact\nCompleted', 'Exact\nOptimal']
    values = [traditional_success/len(results)*100, exact_success/len(results)*100, exact_optimal/len(results)*100]
    
    bars4 = ax4.bar(categories, values, color=[colors['traditional'], colors['exact'], '#2ECC71'], alpha=0.8)
    ax4.set_ylabel('Success Rate (%)')
    ax4.set_title('Algorithm Success Rate', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(values):
        ax4.text(i, v + 1, f'{v:.0f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.suptitle('Traditional vs Exact Branch&Price Comparison', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # 保存图片
    if not os.path.exists('./branch_price_comparison'):
        os.makedirs('./branch_price_comparison')
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./branch_price_comparison/branch_price_comparison_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"Comparison visualization saved: {filename}")
    
    plt.show()
    return fig

def save_results_to_csv(results):
    """保存结果到CSV文件"""
    if not results:
        return
    
    df = pd.DataFrame(results)
    
    if not os.path.exists('./branch_price_comparison'):
        os.makedirs('./branch_price_comparison')
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./branch_price_comparison/branch_price_results_{timestamp}.csv"
    df.to_csv(filename, index=False)
    print(f"Results saved to CSV: {filename}")
    
    return filename

def run_branch_price_comparison():
    """运行分支定价算法对比测试"""
    print("=== Traditional vs Exact Branch&Price Comparison ===")
    print("Comparing two branch-and-price algorithms:")
    print("1. Traditional Branch&Price (Heuristic + Column Generation)")
    print("2. Exact Branch&Price (Complete B&P with Branching)")
    print("="*70)

    # 测试数据配置
    test_configs = [
        {'path': './data/SD2/10x5+mix', 'count': 3, 'name': '10x5_SD2'},
        {'path': './data/SD2/15x10+mix', 'count': 2, 'name': '15x10_SD2'},
    ]

    all_results = []

    for config in test_configs:
        print(f"\n--- Testing {config['name']} instances ---")

        if not os.path.exists(config['path']):
            print(f"Path {config['path']} not found, skipping...")
            continue

        instance_files = [f for f in os.listdir(config['path']) if f.endswith('.fjs')]
        instance_files.sort()

        test_instances = instance_files[:config['count']]

        for i, instance_file in enumerate(test_instances):
            print(f"\n=== Test {i+1}/{len(test_instances)} ({config['name']}) ===")
            instance_path = os.path.join(config['path'], instance_file)

            try:
                result = test_single_instance(instance_path)
                result['dataset'] = config['name']
                all_results.append(result)

            except Exception as e:
                print(f"  Error: {e}")
                continue

    # 汇总分析
    if all_results:
        print(f"\n" + "="*80)
        print(f"=== BRANCH&PRICE COMPARISON SUMMARY ===")
        print(f"="*80)

        valid_results = [r for r in all_results if r['traditional_makespan'] != float('inf') and r['exact_makespan'] != float('inf')]

        if valid_results:
            # 统计各算法获胜次数
            traditional_wins = sum(1 for r in valid_results if r['better_algorithm'] == 'Traditional')
            exact_wins = sum(1 for r in valid_results if r['better_algorithm'] == 'Exact')
            ties = sum(1 for r in valid_results if r['better_algorithm'] == 'Tie')

            # 计算平均改进
            improvements = [r['improvement'] for r in valid_results]
            avg_improvement = np.mean(improvements)
            best_improvement = max(improvements)
            worst_improvement = min(improvements)

            # 计算平均时间比率
            time_ratios = [r['time_ratio'] for r in valid_results if r['time_ratio'] != float('inf')]
            avg_time_ratio = np.mean(time_ratios) if time_ratios else float('inf')

            print(f"\nAlgorithm Performance Summary:")
            print(f"  Traditional B&P wins: {traditional_wins}/{len(valid_results)}")
            print(f"  Exact B&P wins:       {exact_wins}/{len(valid_results)}")
            print(f"  Ties:                 {ties}/{len(valid_results)}")

            print(f"\nQuality Improvement (Exact vs Traditional):")
            print(f"  Average improvement: {avg_improvement:.2f}%")
            print(f"  Best improvement:    {best_improvement:.2f}%")
            print(f"  Worst improvement:   {worst_improvement:.2f}%")

            print(f"\nComputational Efficiency:")
            print(f"  Average time ratio:  {avg_time_ratio:.1f}x (Exact/Traditional)")

            # 成功率统计
            total_tests = len(all_results)
            trad_success = sum(1 for r in all_results if r['traditional_status'] == 'completed')
            exact_success = sum(1 for r in all_results if r['exact_status'] == 'completed')
            exact_optimal = sum(1 for r in all_results if r['exact_optimality'])

            print(f"\nSuccess Rate:")
            print(f"  Traditional completed: {trad_success}/{total_tests} ({trad_success/total_tests*100:.1f}%)")
            print(f"  Exact completed:       {exact_success}/{total_tests} ({exact_success/total_tests*100:.1f}%)")
            print(f"  Exact optimal:         {exact_optimal}/{total_tests} ({exact_optimal/total_tests*100:.1f}%)")

            # 详细结果表
            print(f"\nDetailed Results:")
            print(f"{'Instance':<20} {'Traditional':<12} {'Exact':<12} {'Improve%':<10} {'TimeRatio':<10} {'Winner':<12}")
            print("-" * 85)
            for r in valid_results:
                print(f"{r['instance'][:19]:<20} {r['traditional_makespan']:<12.2f} {r['exact_makespan']:<12.2f} "
                      f"{r['improvement']:<10.2f} {r['time_ratio']:<10.1f} {r['better_algorithm']:<12}")

        # 保存结果和创建可视化
        save_results_to_csv(all_results)
        create_comparison_visualization(all_results)

    return all_results

def main():
    """主函数"""
    results = run_branch_price_comparison()

    if results:
        print(f"\n✓ Branch&Price comparison completed successfully!")
        print(f"  Tested {len(results)} instances across different datasets")
        print(f"  Results and visualizations saved in './branch_price_comparison/' folder")

        # 简要总结
        valid_results = [r for r in results if r['traditional_makespan'] != float('inf') and r['exact_makespan'] != float('inf')]
        if valid_results:
            exact_wins = sum(1 for r in valid_results if r['better_algorithm'] == 'Exact')
            avg_improvement = np.mean([r['improvement'] for r in valid_results])
            print(f"\nKey Findings:")
            print(f"  • Exact B&P won {exact_wins}/{len(valid_results)} instances")
            print(f"  • Average quality improvement: {avg_improvement:.2f}%")
            print(f"  • Exact algorithm provides theoretical optimality guarantee")
    else:
        print(f"\n✗ No successful tests!")

if __name__ == "__main__":
    main()
