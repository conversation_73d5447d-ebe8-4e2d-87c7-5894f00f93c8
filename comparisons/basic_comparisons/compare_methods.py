#!/usr/bin/env python3
"""
对比原始DRL和DRL+分支定价的改进效果
"""

import time
import torch
import numpy as np
import os
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from params import configs
from data_utils import text_to_matrix
from visualization import ImprovementVisualizer, create_improvement_report
from common_utils import heuristic_select_action
import copy

def run_original_drl(env, model, method='SPT', max_steps=1000):
    """
    运行原始DRL方法（纯贪心或启发式）
    """
    start_time = time.time()
    
    # 复制环境
    test_env = copy.deepcopy(env)
    schedule = []
    
    step = 0
    done = False
    
    while not done and step < max_steps:
        # 使用启发式方法选择动作
        action = heuristic_select_action(method, test_env)
        
        # 记录调度决策
        chosen_job = action // test_env.number_of_machines
        chosen_mch = action % test_env.number_of_machines
        chosen_op = test_env.candidate[0, chosen_job]
        
        # 计算时间
        start_op_time = max(test_env.candidate_free_time[0, chosen_job], 
                           test_env.mch_free_time[0, chosen_mch])
        processing_time = test_env.true_op_pt[0, chosen_op, chosen_mch]
        end_op_time = start_op_time + processing_time
        
        schedule.append({
            "op_id": chosen_op,
            "job_id": chosen_job,
            "mch_id": chosen_mch,
            "start_time": start_op_time,
            "end_time": end_op_time,
            "processing_time": processing_time
        })
        
        # 执行动作
        state, reward, done_array = test_env.step(np.array([action]))
        done = done_array[0]
        step += 1
    
    total_time = time.time() - start_time
    makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
    
    return {
        'makespan': makespan,
        'time': total_time,
        'schedule': schedule,
        'method': f'Original_DRL_{method}'
    }

def run_drl_branch_price(env, model, column_generator, master_solver, max_iterations=20):
    """
    运行DRL+分支定价方法
    """
    start_time = time.time()
    
    iteration = 0
    total_columns = 0
    previous_obj = None

    while iteration < max_iterations:
        iteration += 1
        
        # 列生成
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=8)
        
        if not new_columns:
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        # 求解主问题
        solution, reduced_cost = master_solver.solve()
        
        # 改进的收敛判断
        if solution is None:
            break

        # 计算实际的最小reduced cost
        actual_min_rc = min(col.get('reduced_cost', 0.0) for col in new_columns)

        # 基于目标值改善的收敛判断
        current_obj = solution[0]['cost'] if solution else float('inf')
        if iteration > 1 and previous_obj is not None and previous_obj > 0:
            improvement = (previous_obj - current_obj) / previous_obj
            if improvement < 0.001:  # 0.1%改善阈值
                break

        previous_obj = current_obj

        # 基于reduced cost的收敛判断
        if actual_min_rc >= -0.01:  # 调整收敛阈值
            break
    
    total_time = time.time() - start_time
    
    # 获取最佳调度
    best_makespan = float('inf')
    best_schedule = None
    
    if solution:
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
    
    return {
        'makespan': best_makespan,
        'time': total_time,
        'schedule': best_schedule,
        'iterations': iteration,
        'total_columns': total_columns,
        'method': 'DRL_BranchPrice'
    }

def compare_single_instance(instance_path):
    """
    对比单个实例的两种方法
    """
    print(f"Comparing methods on: {os.path.basename(instance_path)}")
    
    # 读取实例
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    configs.max_iterations = 15
    configs.cg_topk = 8
    
    # 初始化组件
    model = DANIEL(configs)
    model.eval()
    
    # 创建环境
    env = FJSPEnvForSameOpNums(n_j=n_j, n_m=n_m)
    env.set_initial_data([job_length], [op_pt])
    
    results = {}
    
    # 1. 运行原始DRL方法（多种启发式）
    print("  Running Original DRL methods...")
    heuristic_methods = ['SPT', 'FIFO', 'MOR', 'MWKR']
    best_original = None
    best_original_makespan = float('inf')
    
    for method in heuristic_methods:
        result = run_original_drl(env, model, method)
        if result['makespan'] < best_original_makespan:
            best_original_makespan = result['makespan']
            best_original = result
    
    results['original'] = best_original
    print(f"    Best Original DRL: {best_original['makespan']:.3f} (time: {best_original['time']:.3f}s)")
    
    # 2. 运行DRL+分支定价方法
    print("  Running DRL + Branch&Price...")
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)
    
    result_bp = run_drl_branch_price(env, model, column_generator, master_solver)
    results['improved'] = result_bp
    print(f"    DRL+Branch&Price: {result_bp['makespan']:.3f} (time: {result_bp['time']:.3f}s, iters: {result_bp['iterations']})")
    
    # 3. 计算改进效果
    makespan_improvement = (best_original['makespan'] - result_bp['makespan']) / best_original['makespan'] * 100
    time_change = (result_bp['time'] - best_original['time']) / best_original['time'] * 100
    
    print(f"    Improvement: Makespan {makespan_improvement:+.1f}%, Time {time_change:+.1f}%")
    
    return {
        'instance': os.path.basename(instance_path),
        'original': best_original,
        'improved': result_bp,
        'makespan_improvement': makespan_improvement,
        'time_change': time_change
    }

def run_comparison_study():
    """
    运行完整的对比研究
    """
    print("=== DRL vs DRL+Branch&Price Comparison Study ===")
    
    # 获取SD1测试实例
    sd1_path = "./data/SD1/10x5"
    if not os.path.exists(sd1_path):
        print(f"Error: SD1 path {sd1_path} not found!")
        return
    
    instance_files = [f for f in os.listdir(sd1_path) if f.endswith('.fjs')]
    instance_files.sort()
    
    # 选择前几个实例进行对比
    test_instances = instance_files[:5]
    print(f"Testing {len(test_instances)} instances...")
    
    comparison_results = []
    original_results = {}
    improved_results = {}
    
    for i, instance_file in enumerate(test_instances):
        print(f"\n--- Test {i+1}/{len(test_instances)} ---")
        instance_path = os.path.join(sd1_path, instance_file)
        
        try:
            result = compare_single_instance(instance_path)
            comparison_results.append(result)
            
            # 为可视化准备数据
            instance_name = result['instance']
            original_results[instance_name] = {
                'makespan': result['original']['makespan'],
                'time': result['original']['time']
            }
            improved_results[instance_name] = {
                'makespan': result['improved']['makespan'],
                'time': result['improved']['time']
            }
            
        except Exception as e:
            print(f"  Error: {e}")
            continue
    
    # 汇总结果
    if comparison_results:
        print(f"\n=== Comparison Summary ===")
        
        makespan_improvements = [r['makespan_improvement'] for r in comparison_results]
        time_changes = [r['time_change'] for r in comparison_results]
        
        avg_makespan_imp = np.mean(makespan_improvements)
        avg_time_change = np.mean(time_changes)
        
        print(f"Average makespan improvement: {avg_makespan_imp:.1f}%")
        print(f"Average time change: {avg_time_change:.1f}%")
        print(f"Instances with better makespan: {sum(1 for x in makespan_improvements if x > 0)}/{len(makespan_improvements)}")
        
        # 详细结果表
        print(f"\nDetailed Results:")
        print(f"{'Instance':<15} {'Orig_MS':<8} {'Impr_MS':<8} {'MS_Imp%':<8} {'Orig_T':<7} {'Impr_T':<7} {'T_Chg%':<7}")
        print("-" * 70)
        for r in comparison_results:
            print(f"{r['instance'][:14]:<15} {r['original']['makespan']:<8.2f} {r['improved']['makespan']:<8.2f} "
                  f"{r['makespan_improvement']:<8.1f} {r['original']['time']:<7.2f} {r['improved']['time']:<7.2f} "
                  f"{r['time_change']:<7.1f}")
        
        # 生成可视化报告
        print(f"\n=== Generating Improvement Analysis ===")
        try:
            # 找到最佳改进的调度用于展示
            best_improvement = max(comparison_results, key=lambda x: x['makespan_improvement'])
            best_schedule = best_improvement['improved']['schedule']
            
            create_improvement_report(original_results, improved_results, best_schedule)
            print("✓ Improvement analysis completed!")
            
        except Exception as e:
            print(f"✗ Visualization error: {e}")
    
    return comparison_results

def main():
    """
    主函数
    """
    print("DRL + Branch&Price Improvement Analysis")
    print("=" * 50)
    
    try:
        results = run_comparison_study()
        
        if results:
            print("\n" + "=" * 50)
            print("✓ Comparison study completed successfully!")
            print("Check the 'improvement_analysis' folder for detailed charts.")
        else:
            print("✗ No successful comparisons!")
            
    except Exception as e:
        print(f"\n✗ Comparison failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
