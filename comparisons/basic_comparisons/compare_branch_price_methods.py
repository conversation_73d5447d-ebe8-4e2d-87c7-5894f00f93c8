#!/usr/bin/env python3
"""
对比传统分支定价和精确分支定价算法
"""

import os
import time
import numpy as np
from traditional_branch_price import TraditionalBranchPrice
from exact_branch_and_price import ExactBranchAndPrice
from params import configs
from data_utils import text_to_matrix

def compare_methods_on_instance(instance_path):
    """
    在单个实例上对比两种方法
    """
    print(f"\n=== Testing: {os.path.basename(instance_path)} ===")
    
    # 读取实例
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    
    results = {}
    
    # 1. 传统分支定价（列生成）
    print("\n--- Traditional Branch & Price ---")
    try:
        traditional_solver = TraditionalBranchPrice(configs)
        start_time = time.time()
        trad_result = traditional_solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
        trad_time = time.time() - start_time
        
        results['traditional'] = {
            'makespan': trad_result['makespan'],
            'time': trad_time,
            'iterations': trad_result['iterations'],
            'method': 'Traditional_BranchPrice'
        }
        print(f"Traditional B&P: {trad_result['makespan']:.3f} (time: {trad_time:.3f}s, iters: {trad_result['iterations']})")
        
    except Exception as e:
        print(f"Traditional B&P failed: {e}")
        results['traditional'] = {'makespan': float('inf'), 'time': 0, 'method': 'Failed'}
    
    # 2. 精确分支定价
    print("\n--- Exact Branch & Price ---")
    try:
        exact_solver = ExactBranchAndPrice(configs)
        start_time = time.time()
        exact_result = exact_solver.solve(job_length, op_pt, time_limit=60)
        exact_time = time.time() - start_time
        
        results['exact'] = {
            'makespan': exact_result['makespan'],
            'time': exact_time,
            'nodes_processed': exact_result['nodes_processed'],
            'nodes_pruned': exact_result['nodes_pruned'],
            'total_columns': exact_result['total_columns'],
            'optimality_proven': exact_result['optimality_proven'],
            'method': 'Exact_BranchAndPrice'
        }
        print(f"Exact B&P: {exact_result['makespan']:.3f} (time: {exact_time:.3f}s, nodes: {exact_result['nodes_processed']})")
        print(f"  Optimality proven: {exact_result['optimality_proven']}")
        
    except Exception as e:
        print(f"Exact B&P failed: {e}")
        results['exact'] = {'makespan': float('inf'), 'time': 0, 'method': 'Failed'}
    
    # 3. 计算理论下界
    print("\n--- Theoretical Lower Bound ---")
    theoretical_lb = calculate_theoretical_lower_bound(job_length, op_pt)
    results['lower_bound'] = theoretical_lb
    print(f"Theoretical LB: {theoretical_lb:.3f}")
    
    # 4. 分析结果
    print(f"\n--- Analysis ---")
    trad_makespan = results['traditional']['makespan']
    exact_makespan = results['exact']['makespan']
    
    if trad_makespan != float('inf') and exact_makespan != float('inf'):
        improvement = (trad_makespan - exact_makespan) / trad_makespan * 100
        print(f"Exact vs Traditional: {improvement:+.2f}% improvement")
        
        # 与理论下界的差距
        trad_gap = (trad_makespan - theoretical_lb) / theoretical_lb * 100
        exact_gap = (exact_makespan - theoretical_lb) / theoretical_lb * 100
        print(f"Traditional gap to LB: {trad_gap:.2f}%")
        print(f"Exact gap to LB: {exact_gap:.2f}%")
        
        results['improvement'] = improvement
        results['traditional_gap'] = trad_gap
        results['exact_gap'] = exact_gap
    
    return results

def calculate_theoretical_lower_bound(job_length, op_pt):
    """
    计算理论下界
    """
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    # 机器负载下界
    min_processing_times = []
    for op_id in range(n_op):
        op_times = op_pt[op_id][op_pt[op_id] > 0]
        if len(op_times) > 0:
            min_processing_times.append(np.min(op_times))
        else:
            min_processing_times.append(0)
    
    total_work = np.sum(min_processing_times)
    machine_bound = total_work / n_m
    
    # 关键路径下界
    job_min_times = []
    op_idx = 0
    for job_id in range(n_j):
        job_time = 0
        for _ in range(job_length[job_id]):
            if op_idx < n_op:
                op_times = op_pt[op_idx][op_pt[op_idx] > 0]
                if len(op_times) > 0:
                    job_time += np.min(op_times)
                op_idx += 1
        job_min_times.append(job_time)
    
    critical_path_bound = max(job_min_times) if job_min_times else 0
    
    return max(machine_bound, critical_path_bound)

def run_comprehensive_comparison():
    """
    运行全面对比测试
    """
    print("=== Comprehensive Comparison: Traditional vs Exact Branch & Price ===")
    
    # 测试实例
    test_instances = [
        "./data/SD2/10x5+test/10x5+test_001.fjs",
        "./data/SD2/10x5+test/10x5+test_002.fjs", 
        "./data/SD2/10x5+test/10x5+test_003.fjs",
        "./data/SD1/10x5/10x5_01.fjs" if os.path.exists("./data/SD1/10x5/10x5_01.fjs") else None,
    ]
    
    # 过滤存在的文件
    test_instances = [f for f in test_instances if f and os.path.exists(f)]
    
    if not test_instances:
        print("No test instances found!")
        return
    
    all_results = []
    
    for instance_path in test_instances:
        try:
            result = compare_methods_on_instance(instance_path)
            result['instance'] = os.path.basename(instance_path)
            all_results.append(result)
        except Exception as e:
            print(f"Error processing {instance_path}: {e}")
            continue
    
    # 汇总分析
    if all_results:
        print(f"\n" + "="*80)
        print(f"=== COMPREHENSIVE COMPARISON SUMMARY ===")
        print(f"="*80)
        
        # 统计成功的测试
        successful_tests = [r for r in all_results 
                          if r['traditional']['makespan'] != float('inf') 
                          and r['exact']['makespan'] != float('inf')]
        
        if successful_tests:
            improvements = [r.get('improvement', 0) for r in successful_tests]
            trad_gaps = [r.get('traditional_gap', 0) for r in successful_tests]
            exact_gaps = [r.get('exact_gap', 0) for r in successful_tests]
            
            print(f"\nPerformance Summary ({len(successful_tests)} instances):")
            print(f"  Average improvement (Exact vs Traditional): {np.mean(improvements):+.2f}%")
            print(f"  Traditional average gap to LB: {np.mean(trad_gaps):.2f}%")
            print(f"  Exact average gap to LB: {np.mean(exact_gaps):.2f}%")
            
            # 详细结果表
            print(f"\nDetailed Results:")
            print(f"{'Instance':<25} {'Traditional':<12} {'Exact':<12} {'Improvement':<12} {'Exact_Gap':<10}")
            print("-" * 80)
            for r in successful_tests:
                trad_ms = r['traditional']['makespan']
                exact_ms = r['exact']['makespan']
                improvement = r.get('improvement', 0)
                exact_gap = r.get('exact_gap', 0)
                
                print(f"{r['instance'][:24]:<25} {trad_ms:<12.2f} {exact_ms:<12.2f} {improvement:<12.2f} {exact_gap:<10.2f}")
            
            # 关键发现
            print(f"\n=== Key Findings ===")
            if np.mean(exact_gaps) < np.mean(trad_gaps):
                print("✓ Exact Branch & Price consistently finds better solutions")
            else:
                print("⚠ Both methods perform similarly on these instances")
                
            if np.mean(exact_gaps) < 5:
                print("✓ Exact Branch & Price finds near-optimal solutions (< 5% gap)")
            else:
                print("⚠ Solutions may not be optimal (large gap to theoretical LB)")
        
        else:
            print("No successful comparisons!")
    
    return all_results

if __name__ == "__main__":
    results = run_comprehensive_comparison()
    
    if results:
        print(f"\n✓ Comparison completed successfully!")
        print(f"  Tested {len(results)} instances")
    else:
        print(f"\n✗ No successful tests!")
