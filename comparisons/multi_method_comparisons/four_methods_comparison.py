#!/usr/bin/env python3
"""
四种算法全面对比：
1. 传统分支定价（启发式+列生成）
2. 精确分支定价算法  
3. 纯深度强化学习(DRL)
4. DRL+分支定价混合算法
"""

import time
import torch
import numpy as np
import os
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from traditional_branch_price import TraditionalBranchPrice
from exact_branch_and_price import ExactBranchAndPrice
from params import configs
from data_utils import text_to_matrix
from common_utils import heuristic_select_action
from visualization import ImprovementVisualizer
import copy

def run_traditional_branch_price(job_length, op_pt, configs):
    """算法1: 传统分支定价（启发式+列生成）"""
    print("    Running Traditional Branch&Price (Heuristic+Column Generation)...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    traditional_solver = TraditionalBranchPrice(configs)
    result = traditional_solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
    
    total_time = time.time() - start_time
    result['time'] = total_time
    result['algorithm_type'] = 'Heuristic'
    result['optimality_guaranteed'] = False
    
    print(f"      Traditional B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {result['iterations']})")
    return result

def run_exact_branch_price(job_length, op_pt, configs):
    """算法2: 精确分支定价算法"""
    print("    Running Exact Branch&Price (Exact Algorithm)...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    exact_solver = ExactBranchAndPrice(configs)
    result = exact_solver.solve(job_length, op_pt, time_limit=120)
    
    total_time = time.time() - start_time
    result['time'] = total_time
    result['algorithm_type'] = 'Exact'
    result['optimality_guaranteed'] = result.get('optimality_proven', False)
    
    print(f"      Exact B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, nodes: {result.get('nodes_processed', 0)})")
    return result

def run_pure_drl(job_length, op_pt, configs):
    """算法3: 纯深度强化学习(DRL)"""
    print("    Running Pure DRL (DANIEL Model)...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    model = DANIEL(configs)
    model.eval()
    
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    # 测试多种启发式方法
    heuristic_methods = ['SPT', 'FIFO', 'MOR', 'MWKR']
    best_result = None
    best_makespan = float('inf')
    
    for method in heuristic_methods:
        test_env = copy.deepcopy(env)
        schedule = []
        
        step = 0
        done = False
        max_steps = 1000
        
        while not done and step < max_steps:
            action = heuristic_select_action(method, test_env)
            
            chosen_job = action // test_env.number_of_machines
            chosen_mch = action % test_env.number_of_machines
            chosen_op = test_env.candidate[0, chosen_job]
            
            start_op_time = max(test_env.candidate_free_time[0, chosen_job], 
                               test_env.mch_free_time[0, chosen_mch])
            processing_time = test_env.true_op_pt[0, chosen_op, chosen_mch]
            end_op_time = start_op_time + processing_time
            
            schedule.append({
                "op_id": chosen_op,
                "job_id": chosen_job,
                "mch_id": chosen_mch,
                "start_time": start_op_time,
                "end_time": end_op_time,
                "processing_time": processing_time
            })
            
            state, reward, done_array = test_env.step(np.array([action]))
            done = done_array[0]
            step += 1
        
        makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
        
        if makespan < best_makespan:
            best_makespan = makespan
            best_result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': f'Pure_DRL_{method}',
                'algorithm_type': 'Machine Learning',
                'optimality_guaranteed': False
            }
    
    total_time = time.time() - start_time
    best_result['time'] = total_time
    
    print(f"      Pure DRL: {best_result['makespan']:.3f} (time: {total_time:.3f}s)")
    return best_result

def run_drl_branch_price(job_length, op_pt, configs):
    """算法4: DRL+分支定价混合算法"""
    print("    Running DRL + Branch&Price (Hybrid Algorithm)...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    configs.max_iterations = 15
    configs.cg_topk = 6
    
    model = DANIEL(configs)
    model.eval()
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)
    
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    iteration = 0
    total_columns = 0
    previous_obj = None

    while iteration < configs.max_iterations:
        iteration += 1
        
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
        
        if not new_columns:
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        solution, reduced_cost = master_solver.solve()
        
        # 改进的收敛判断
        if solution is None:
            break

        # 计算实际的最小reduced cost
        actual_min_rc = min(col.get('reduced_cost', 0.0) for col in new_columns)

        # 基于目标值改善的收敛判断
        current_obj = solution[0]['cost'] if solution else float('inf')
        if iteration > 1 and previous_obj is not None and previous_obj > 0:
            improvement = (previous_obj - current_obj) / previous_obj
            if improvement < 0.001:  # 0.1%改善阈值
                break

        previous_obj = current_obj

        # 基于reduced cost的收敛判断
        if actual_min_rc >= -0.01:  # 调整收敛阈值
            break
    
    total_time = time.time() - start_time
    
    best_makespan = float('inf')
    best_schedule = None
    
    if solution:
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
    
    result = {
        'makespan': best_makespan,
        'time': total_time,
        'schedule': best_schedule,
        'iterations': iteration,
        'total_columns': total_columns,
        'method': 'DRL_BranchPrice',
        'algorithm_type': 'Hybrid',
        'optimality_guaranteed': False
    }
    
    print(f"      DRL+B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {iteration})")
    return result

def test_single_instance(instance_path):
    """在单个实例上测试四种算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")
    
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    results = {}
    
    # 算法1: 传统分支定价
    try:
        results['traditional'] = run_traditional_branch_price(job_length, op_pt, configs)
    except Exception as e:
        print(f"      Traditional B&P failed: {e}")
        results['traditional'] = {'makespan': float('inf'), 'time': 0, 'method': 'Traditional_Failed'}
    
    # 算法2: 精确分支定价
    try:
        results['exact'] = run_exact_branch_price(job_length, op_pt, configs)
    except Exception as e:
        print(f"      Exact B&P failed: {e}")
        results['exact'] = {'makespan': float('inf'), 'time': 0, 'method': 'Exact_Failed'}
    
    # 算法3: 纯DRL
    try:
        results['drl'] = run_pure_drl(job_length, op_pt, configs)
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        results['drl'] = {'makespan': float('inf'), 'time': 0, 'method': 'Pure_DRL_Failed'}
    
    # 算法4: DRL+分支定价
    try:
        results['hybrid'] = run_drl_branch_price(job_length, op_pt, configs)
    except Exception as e:
        print(f"      DRL+B&P failed: {e}")
        results['hybrid'] = {'makespan': float('inf'), 'time': 0, 'method': 'DRL_BranchPrice_Failed'}
    
    # 分析结果
    makespans = {k: v['makespan'] for k, v in results.items() if v['makespan'] != float('inf')}
    times = {k: v['time'] for k, v in results.items()}
    
    if makespans:
        best_makespan = min(makespans.values())
        best_algorithm = min(makespans, key=makespans.get)
        
        print(f"  Results Summary:")
        print(f"    Traditional B&P: {results['traditional']['makespan']:.3f} ({results['traditional']['time']:.3f}s)")
        print(f"    Exact B&P:       {results['exact']['makespan']:.3f} ({results['exact']['time']:.3f}s)")
        print(f"    Pure DRL:        {results['drl']['makespan']:.3f} ({results['drl']['time']:.3f}s)")
        print(f"    DRL+B&P:         {results['hybrid']['makespan']:.3f} ({results['hybrid']['time']:.3f}s)")
        print(f"    Best: {best_algorithm} with makespan {best_makespan:.3f}")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'results': results,
        'best_algorithm': best_algorithm if makespans else None,
        'best_makespan': best_makespan if makespans else float('inf')
    }

def run_four_methods_comparison():
    """运行四种方法的完整对比研究"""
    print("=== Four Methods Comprehensive Comparison ===")
    print("1. Traditional Branch&Price (Heuristic+Column Generation)")
    print("2. Exact Branch&Price (Exact Algorithm)")
    print("3. Pure DRL (DANIEL Model)")
    print("4. DRL+Branch&Price (Hybrid Algorithm)")
    print("="*60)
    
    # 测试实例
    test_configs = [
        {'path': './data/SD2/10x5+test', 'count': 3, 'name': '10x5_SD2'},
    ]
    
    all_results = []
    
    for config in test_configs:
        print(f"\n--- Testing {config['name']} instances ---")
        
        if not os.path.exists(config['path']):
            print(f"Path {config['path']} not found, skipping...")
            continue
        
        instance_files = [f for f in os.listdir(config['path']) if f.endswith('.fjs')]
        instance_files.sort()
        
        test_instances = instance_files[:config['count']]
        
        for i, instance_file in enumerate(test_instances):
            print(f"\n=== Test {i+1}/{len(test_instances)} ({config['name']}) ===")
            instance_path = os.path.join(config['path'], instance_file)
            
            try:
                result = test_single_instance(instance_path)
                result['dataset'] = config['name']
                all_results.append(result)
                
            except Exception as e:
                print(f"  Error: {e}")
                continue
    
    # 汇总分析
    if all_results:
        print(f"\n" + "="*80)
        print(f"=== FOUR METHODS COMPREHENSIVE SUMMARY ===")
        print(f"="*80)
        
        # 统计各算法获胜次数
        algorithm_wins = {'traditional': 0, 'exact': 0, 'drl': 0, 'hybrid': 0}
        valid_results = [r for r in all_results if r['best_algorithm']]
        
        for result in valid_results:
            if result['best_algorithm'] in algorithm_wins:
                algorithm_wins[result['best_algorithm']] += 1
        
        print(f"\nAlgorithm Performance Summary:")
        print(f"  Traditional B&P wins: {algorithm_wins['traditional']}/{len(valid_results)}")
        print(f"  Exact B&P wins:       {algorithm_wins['exact']}/{len(valid_results)}")
        print(f"  Pure DRL wins:        {algorithm_wins['drl']}/{len(valid_results)}")
        print(f"  DRL+B&P wins:         {algorithm_wins['hybrid']}/{len(valid_results)}")
        
        # 详细结果表
        print(f"\nDetailed Results:")
        print(f"{'Instance':<20} {'Traditional':<12} {'Exact':<12} {'DRL':<12} {'Hybrid':<12} {'Best':<12}")
        print("-" * 85)
        for r in all_results:
            trad = r['results']['traditional']['makespan']
            exact = r['results']['exact']['makespan']
            drl = r['results']['drl']['makespan']
            hybrid = r['results']['hybrid']['makespan']
            best = r.get('best_algorithm', 'N/A')
            
            print(f"{r['instance'][:19]:<20} {trad:<12.2f} {exact:<12.2f} {drl:<12.2f} {hybrid:<12.2f} {best:<12}")
    
    return all_results

if __name__ == "__main__":
    results = run_four_methods_comparison()
    
    if results:
        print(f"\n✓ Four methods comparison completed successfully!")
        print(f"  Tested {len(results)} instances across all algorithms")
    else:
        print(f"\n✗ No successful tests!")
