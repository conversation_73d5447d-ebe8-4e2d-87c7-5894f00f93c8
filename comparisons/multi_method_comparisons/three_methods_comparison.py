#!/usr/bin/env python3
"""
三种方法对比：传统分支定价 vs 纯DRL vs DRL+分支定价
"""

import time
import torch
import numpy as np
import os
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from traditional_branch_price import TraditionalBranchPrice
from params import configs
from data_utils import text_to_matrix
from common_utils import heuristic_select_action
from visualization import ImprovementVisualizer
import copy

def run_traditional_branch_price(job_length, op_pt, configs):
    """
    运行传统分支定价方法
    """
    print("    Running Traditional Branch&Price...")
    start_time = time.time()
    
    # 更新配置
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    # 创建传统分支定价求解器
    traditional_solver = TraditionalBranchPrice(configs)
    
    # 求解
    result = traditional_solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
    
    total_time = time.time() - start_time
    result['time'] = total_time
    
    print(f"      Traditional B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {result['iterations']})")
    return result

def run_pure_drl(job_length, op_pt, configs):
    """
    运行纯DRL方法（多种启发式的最佳结果）
    """
    print("    Running Pure DRL...")
    start_time = time.time()
    
    # 更新配置
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    # 初始化模型和环境
    model = DANIEL(configs)
    model.eval()
    
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    # 测试多种启发式方法
    heuristic_methods = ['SPT', 'FIFO', 'MOR', 'MWKR']
    best_result = None
    best_makespan = float('inf')
    
    for method in heuristic_methods:
        # 复制环境
        test_env = copy.deepcopy(env)
        schedule = []
        
        step = 0
        done = False
        max_steps = 1000
        
        while not done and step < max_steps:
            # 使用启发式方法选择动作
            action = heuristic_select_action(method, test_env)
            
            # 记录调度决策
            chosen_job = action // test_env.number_of_machines
            chosen_mch = action % test_env.number_of_machines
            chosen_op = test_env.candidate[0, chosen_job]
            
            # 计算时间
            start_op_time = max(test_env.candidate_free_time[0, chosen_job], 
                               test_env.mch_free_time[0, chosen_mch])
            processing_time = test_env.true_op_pt[0, chosen_op, chosen_mch]
            end_op_time = start_op_time + processing_time
            
            schedule.append({
                "op_id": chosen_op,
                "job_id": chosen_job,
                "mch_id": chosen_mch,
                "start_time": start_op_time,
                "end_time": end_op_time,
                "processing_time": processing_time
            })
            
            # 执行动作
            state, reward, done_array = test_env.step(np.array([action]))
            done = done_array[0]
            step += 1
        
        # 计算makespan
        makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
        
        if makespan < best_makespan:
            best_makespan = makespan
            best_result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': f'Pure_DRL_{method}'
            }
    
    total_time = time.time() - start_time
    best_result['time'] = total_time
    
    print(f"      Pure DRL: {best_result['makespan']:.3f} (time: {total_time:.3f}s)")
    return best_result

def run_drl_branch_price(job_length, op_pt, configs):
    """
    运行DRL+分支定价方法
    """
    print("    Running DRL + Branch&Price...")
    start_time = time.time()
    
    # 更新配置
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    configs.max_iterations = 15
    configs.cg_topk = 6
    
    # 初始化组件
    model = DANIEL(configs)
    model.eval()
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)
    
    # 创建环境
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    # 列生成迭代
    iteration = 0
    total_columns = 0
    previous_obj = None

    while iteration < configs.max_iterations:
        iteration += 1
        
        # 列生成
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
        
        if not new_columns:
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        # 求解主问题
        solution, reduced_cost = master_solver.solve()
        
        # 改进的收敛判断
        if solution is None:
            print(f"      Master problem infeasible at iteration {iteration}")
            break

        # 计算实际的最小reduced cost
        actual_min_rc = min(col.get('reduced_cost', 0.0) for col in new_columns)

        # 基于目标值改善的收敛判断
        current_obj = solution[0]['cost'] if solution else float('inf')
        if iteration > 1 and previous_obj is not None and previous_obj > 0:
            improvement = (previous_obj - current_obj) / previous_obj
            if improvement < 0.001:  # 0.1%改善阈值
                print(f"      Converged by objective improvement at iteration {iteration}")
                break

        previous_obj = current_obj

        # 基于reduced cost的收敛判断
        if actual_min_rc >= -0.01:  # 调整收敛阈值
            print(f"      Converged by reduced cost at iteration {iteration}")
            break
    
    total_time = time.time() - start_time
    
    # 获取最佳调度
    best_makespan = float('inf')
    best_schedule = None
    
    if solution:
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
    
    result = {
        'makespan': best_makespan,
        'time': total_time,
        'schedule': best_schedule,
        'iterations': iteration,
        'total_columns': total_columns,
        'method': 'DRL_BranchPrice'
    }
    
    print(f"      DRL+B&P: {result['makespan']:.3f} (time: {total_time:.3f}s, iters: {iteration})")
    return result

def test_single_instance(instance_path):
    """
    在单个实例上测试三种方法
    """
    print(f"\nTesting: {os.path.basename(instance_path)}")
    
    # 读取实例
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    results = {}
    
    try:
        # 1. 传统分支定价
        result_traditional = run_traditional_branch_price(job_length, op_pt, configs)
        results['traditional'] = result_traditional
    except Exception as e:
        print(f"      Traditional B&P failed: {e}")
        results['traditional'] = {'makespan': float('inf'), 'time': 0, 'method': 'Traditional_BranchPrice_Failed'}
    
    try:
        # 2. 纯DRL
        result_drl = run_pure_drl(job_length, op_pt, configs)
        results['drl'] = result_drl
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        results['drl'] = {'makespan': float('inf'), 'time': 0, 'method': 'Pure_DRL_Failed'}
    
    try:
        # 3. DRL+分支定价
        result_hybrid = run_drl_branch_price(job_length, op_pt, configs)
        results['hybrid'] = result_hybrid
    except Exception as e:
        print(f"      DRL+B&P failed: {e}")
        results['hybrid'] = {'makespan': float('inf'), 'time': 0, 'method': 'DRL_BranchPrice_Failed'}
    
    # 计算改进效果
    traditional_ms = results['traditional']['makespan']
    drl_ms = results['drl']['makespan']
    hybrid_ms = results['hybrid']['makespan']
    
    # 以传统分支定价为基准
    drl_vs_traditional = ((traditional_ms - drl_ms) / traditional_ms * 100) if traditional_ms != float('inf') else 0
    hybrid_vs_traditional = ((traditional_ms - hybrid_ms) / traditional_ms * 100) if traditional_ms != float('inf') else 0
    hybrid_vs_drl = ((drl_ms - hybrid_ms) / drl_ms * 100) if drl_ms != float('inf') else 0
    
    print(f"  Results:")
    print(f"    Traditional B&P: {traditional_ms:.3f} ({results['traditional']['time']:.3f}s)")
    print(f"    Pure DRL:        {drl_ms:.3f} ({results['drl']['time']:.3f}s)")
    print(f"    DRL+B&P:         {hybrid_ms:.3f} ({results['hybrid']['time']:.3f}s)")
    print(f"  Improvements:")
    print(f"    DRL vs Traditional:    {drl_vs_traditional:+.1f}%")
    print(f"    Hybrid vs Traditional: {hybrid_vs_traditional:+.1f}%")
    print(f"    Hybrid vs DRL:         {hybrid_vs_drl:+.1f}%")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'results': results,
        'improvements': {
            'drl_vs_traditional': drl_vs_traditional,
            'hybrid_vs_traditional': hybrid_vs_traditional,
            'hybrid_vs_drl': hybrid_vs_drl
        }
    }

def run_three_methods_comparison():
    """
    运行三种方法的完整对比研究
    """
    print("=== Three Methods Comparison: Traditional B&P vs Pure DRL vs DRL+B&P ===")
    
    # 测试SD2数据集的小规模实例
    test_configs = [
        {'path': './data/SD2/10x5+test', 'count': 5, 'name': '10x5'},
        {'path': './data/SD1/10x5', 'count': 3, 'name': 'SD1_10x5'},
    ]
    
    all_results = []
    
    for config in test_configs:
        print(f"\n--- Testing {config['name']} instances ---")
        
        if not os.path.exists(config['path']):
            print(f"Path {config['path']} not found, skipping...")
            continue
        
        instance_files = [f for f in os.listdir(config['path']) if f.endswith('.fjs')]
        instance_files.sort()
        
        # 选择前几个实例
        test_instances = instance_files[:config['count']]
        
        for i, instance_file in enumerate(test_instances):
            print(f"\n=== Test {i+1}/{len(test_instances)} ({config['name']}) ===")
            instance_path = os.path.join(config['path'], instance_file)
            
            try:
                result = test_single_instance(instance_path)
                result['dataset'] = config['name']
                all_results.append(result)
                
            except Exception as e:
                print(f"  Error: {e}")
                continue
    
    # 汇总分析
    if all_results:
        print(f"\n" + "="*80)
        print(f"=== COMPREHENSIVE COMPARISON SUMMARY ===")
        print(f"="*80)
        
        # 统计各方法的表现
        traditional_makespans = [r['results']['traditional']['makespan'] for r in all_results 
                                if r['results']['traditional']['makespan'] != float('inf')]
        drl_makespans = [r['results']['drl']['makespan'] for r in all_results 
                        if r['results']['drl']['makespan'] != float('inf')]
        hybrid_makespans = [r['results']['hybrid']['makespan'] for r in all_results 
                           if r['results']['hybrid']['makespan'] != float('inf')]
        
        traditional_times = [r['results']['traditional']['time'] for r in all_results]
        drl_times = [r['results']['drl']['time'] for r in all_results]
        hybrid_times = [r['results']['hybrid']['time'] for r in all_results]
        
        print(f"\nMethod Performance Summary:")
        print(f"  Traditional B&P: avg makespan {np.mean(traditional_makespans):.2f}, avg time {np.mean(traditional_times):.2f}s")
        print(f"  Pure DRL:        avg makespan {np.mean(drl_makespans):.2f}, avg time {np.mean(drl_times):.2f}s")
        print(f"  DRL+B&P:         avg makespan {np.mean(hybrid_makespans):.2f}, avg time {np.mean(hybrid_times):.2f}s")
        
        # 改进统计
        drl_vs_trad_improvements = [r['improvements']['drl_vs_traditional'] for r in all_results]
        hybrid_vs_trad_improvements = [r['improvements']['hybrid_vs_traditional'] for r in all_results]
        hybrid_vs_drl_improvements = [r['improvements']['hybrid_vs_drl'] for r in all_results]
        
        print(f"\nImprovement Statistics:")
        print(f"  DRL vs Traditional B&P:    avg {np.mean(drl_vs_trad_improvements):+.1f}% (success rate: {sum(1 for x in drl_vs_trad_improvements if x > 0)}/{len(drl_vs_trad_improvements)})")
        print(f"  Hybrid vs Traditional B&P: avg {np.mean(hybrid_vs_trad_improvements):+.1f}% (success rate: {sum(1 for x in hybrid_vs_trad_improvements if x > 0)}/{len(hybrid_vs_trad_improvements)})")
        print(f"  Hybrid vs Pure DRL:        avg {np.mean(hybrid_vs_drl_improvements):+.1f}% (success rate: {sum(1 for x in hybrid_vs_drl_improvements if x > 0)}/{len(hybrid_vs_drl_improvements)})")
        
        # 详细结果表
        print(f"\nDetailed Results:")
        print(f"{'Instance':<20} {'Dataset':<10} {'Traditional':<12} {'DRL':<12} {'Hybrid':<12} {'H_vs_T':<8} {'H_vs_D':<8}")
        print("-" * 90)
        for r in all_results:
            trad_ms = r['results']['traditional']['makespan']
            drl_ms = r['results']['drl']['makespan']
            hybrid_ms = r['results']['hybrid']['makespan']
            h_vs_t = r['improvements']['hybrid_vs_traditional']
            h_vs_d = r['improvements']['hybrid_vs_drl']
            
            print(f"{r['instance'][:19]:<20} {r['dataset']:<10} {trad_ms:<12.2f} {drl_ms:<12.2f} {hybrid_ms:<12.2f} {h_vs_t:<8.1f} {h_vs_d:<8.1f}")

        # 生成可视化报告
        print(f"\n=== Generating Three Methods Comparison Visualization ===")
        try:
            visualizer = ImprovementVisualizer()
            visualizer.draw_three_methods_comparison(all_results)
            print("✓ Three methods comparison visualization completed!")

        except Exception as e:
            print(f"✗ Visualization error: {e}")

    return all_results

def main():
    """
    主函数
    """
    print("Three Methods Comparison Study")
    print("=" * 50)
    
    try:
        results = run_three_methods_comparison()
        
        if results:
            print("\n" + "=" * 50)
            print("✓ Three methods comparison completed successfully!")
            
            # 关键发现
            hybrid_vs_drl_improvements = [r['improvements']['hybrid_vs_drl'] for r in results]
            hybrid_vs_trad_improvements = [r['improvements']['hybrid_vs_traditional'] for r in results]
            
            avg_hybrid_vs_drl = np.mean(hybrid_vs_drl_improvements)
            avg_hybrid_vs_trad = np.mean(hybrid_vs_trad_improvements)
            
            print("\nKey Findings:")
            print(f"  • DRL+B&P vs Pure DRL:        {avg_hybrid_vs_drl:+.1f}% average improvement")
            print(f"  • DRL+B&P vs Traditional B&P: {avg_hybrid_vs_trad:+.1f}% average improvement")
            print(f"  • Tested on {len(results)} instances across multiple datasets")
            
        else:
            print("✗ No successful tests!")
            
    except Exception as e:
        print(f"\n✗ Comparison failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
