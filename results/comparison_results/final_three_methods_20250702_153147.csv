instance,size,traditional_makespan,drl_makespan,hybrid_makespan,traditional_time,drl_time,hybrid_time,best_algorithm,improvements,traditional_method,drl_method,hybrid_method,hybrid_iterations,hybrid_columns
10x5+test_001.fjs,10x5,467.0,99.70707070699929,99.85858585819611,0.015698671340942383,0.15260720252990723,0.694159746170044,drl,"{'drl_vs_traditional': np.float64(78.64944952740916), 'hybrid_vs_traditional': np.float64(78.61700516955116), 'hybrid_vs_drl': np.float64(-0.15196028739231862)}",Traditional_BranchPrice,DRL_Model,Improved_DRL_BranchPrice_2iter,2,8
10x5+test_002.fjs,10x5,344.0,98.34343434339965,98.0,0.014152765274047852,0.15097546577453613,0.6250119209289551,hybrid,"{'drl_vs_traditional': np.float64(71.41179234203499), 'hybrid_vs_traditional': np.float64(71.51162790697676), 'hybrid_vs_drl': np.float64(0.3492193919122573)}",Traditional_BranchPrice,DRL_Model,Improved_DRL_BranchPrice_2iter,2,8
10x5+test_003.fjs,10x5,390.0,92.27272727269973,90.39393939369758,0.014632463455200195,0.15085363388061523,0.6266517639160156,hybrid,"{'drl_vs_traditional': np.float64(76.34032634033339), 'hybrid_vs_traditional': np.float64(76.82206682212883), 'hybrid_vs_drl': np.float64(2.0361247949783037)}",Traditional_BranchPrice,DRL_Model,Improved_DRL_BranchPrice_2iter,2,8
10x5+test_004.fjs,10x5,409.0,93.32323232319968,97.84848484829813,0.014341354370117188,0.14823102951049805,0.6295075416564941,drl,"{'drl_vs_traditional': np.float64(77.18258378405875), 'hybrid_vs_traditional': np.float64(76.07616507376574), 'hybrid_vs_drl': np.float64(-4.849009632913773)}",Traditional_BranchPrice,DRL_Model,Improved_DRL_BranchPrice_2iter,2,8
10x5+test_005.fjs,10x5,476.0,97.06060606059994,99.79797979769717,0.014593124389648438,0.15248370170593262,0.6291515827178955,drl,"{'drl_vs_traditional': np.float64(79.60911637382354), 'hybrid_vs_traditional': np.float64(79.03403785762664), 'hybrid_vs_drl': np.float64(-2.8202726607622313)}",Traditional_BranchPrice,DRL_Model,Improved_DRL_BranchPrice_2iter,2,8
