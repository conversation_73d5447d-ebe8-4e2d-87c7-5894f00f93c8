instance,size,n_operations,drl_makespan,enhanced_makespan,drl_time,enhanced_time,improvement,time_ratio,best_algorithm,drl_method,enhanced_method,enhanced_iterations,enhanced_columns,drl_steps,convergence_history
20x10+mix_001.fjs,20x10,200,102.27272727229568,97.13131313089583,1.5830299854278564,22.944728136062622,5.0271604938344,14.494184157769565,enhanced_hybrid,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(97.13131313089583), 'min_reduced_cost': np.float64(194.26262626179167), 'columns_added': 20, 'best_makespan': 97.13131313089583, 'iter_time': 22.94162631034851}]"
20x10+mix_002.fjs,20x10,200,103.22222222169472,104.2424242417937,1.5329766273498535,22.721104860305786,-0.9883550248587429,14.82155986917106,drl,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(104.2424242417937), 'min_reduced_cost': np.float64(200.0), 'columns_added': 20, 'best_makespan': 104.2424242417937, 'iter_time': 22.71832275390625}]"
20x10+mix_003.fjs,20x10,200,101.71717171679624,96.42424242389654,1.5564055442810059,22.80202889442444,5.203574975163899,14.650441832599627,enhanced_hybrid,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(96.42424242389654), 'min_reduced_cost': np.float64(192.8484848477931), 'columns_added': 20, 'best_makespan': 96.42424242389654, 'iter_time': 22.799206733703613}]"
20x10+mix_004.fjs,20x10,200,97.80808080779717,98.66666666619528,1.5302364826202393,22.88037133216858,-0.8778271195049042,14.952180000956643,drl,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(98.66666666619528), 'min_reduced_cost': np.float64(197.33333333239057), 'columns_added': 20, 'best_makespan': 98.66666666619528, 'iter_time': 22.877540826797485}]"
20x10+mix_005.fjs,20x10,200,98.48484848439547,100.61616161549331,1.562239646911621,22.935094118118286,-2.1641025638939158,14.680906455970751,drl,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(100.61616161549331), 'min_reduced_cost': np.float64(200.0), 'columns_added': 20, 'best_makespan': 100.61616161549331, 'iter_time': 22.932251691818237}]"
20x10+mix_006.fjs,20x10,200,101.75757575699419,103.72727272669421,1.5328595638275146,22.861448049545288,-1.9356759976316942,14.91424823841056,drl,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(103.72727272669421), 'min_reduced_cost': np.float64(200.0), 'columns_added': 20, 'best_makespan': 103.72727272669421, 'iter_time': 22.85868215560913}]"
20x10+mix_007.fjs,20x10,200,102.6666666662963,102.54545454509642,1.545367956161499,22.923616886138916,0.11806375441588848,14.833759684702093,enhanced_hybrid,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(102.54545454509642), 'min_reduced_cost': np.float64(200.0), 'columns_added': 20, 'best_makespan': 102.54545454509642, 'iter_time': 22.920758724212646}]"
20x10+mix_008.fjs,20x10,200,94.0,94.45454545429752,1.5381035804748535,22.753061532974243,-0.4835589939335282,14.792931907713113,drl,Pure_DRL,Enhanced_DRL_BranchPrice_1iter,1,20,200,"[{'iteration': 1, 'objective': np.float64(94.45454545429752), 'min_reduced_cost': np.float64(188.90909090859503), 'columns_added': 20, 'best_makespan': 94.45454545429752, 'iter_time': 22.750279426574707}]"
