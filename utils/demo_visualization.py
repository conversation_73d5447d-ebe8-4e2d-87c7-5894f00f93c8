#!/usr/bin/env python3
"""
可视化功能演示脚本
"""

import time
import torch
import numpy as np
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from params import configs
from data_utils import SD2_instance_generator
from visualization import ColumnGenerationVisualizer

def demo_visualization():
    """
    演示可视化功能
    """
    print("=== Column Generation Visualization Demo ===")
    
    # 配置小规模实例便于演示
    configs.n_j = 5
    configs.n_m = 3
    configs.n_op = 15
    configs.max_iterations = 15
    configs.cg_topk = 6
    
    # 初始化组件
    model = DANIEL(configs)
    model.eval()
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)
    visualizer = ColumnGenerationVisualizer()
    
    # 生成测试实例
    job_length, op_pt, _ = SD2_instance_generator(configs)
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
    env.set_initial_data([job_length], [op_pt])
    
    print(f"Demo instance: {configs.n_j} jobs, {configs.n_m} machines, {configs.n_op} operations")
    print(f"Job lengths: {job_length}")
    
    # 运行列生成并记录数据
    start_time = time.time()
    iteration = 0
    total_columns = 0
    objective_history = []
    reduced_cost_history = []
    
    print(f"\nRunning column generation...")
    
    while iteration < configs.max_iterations:
        iteration += 1
        
        # 列生成
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
        
        if not new_columns:
            print(f"Iteration {iteration}: No new columns - converged!")
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        # 求解主问题
        solution, reduced_cost = master_solver.solve()
        obj_value = master_solver.solver.Objective().Value()
        objective_history.append(obj_value)
        reduced_cost_history.append(reduced_cost)
        
        print(f"Iteration {iteration}: {len(new_columns)} columns, obj={obj_value:.3f}, rc={reduced_cost:.5f}")
        
        # 检查收敛
        if reduced_cost >= -1e-4:
            print(f"Converged at iteration {iteration}!")
            break
    
    total_time = time.time() - start_time
    
    # 获取最佳调度
    best_schedule = None
    best_makespan = float('inf')
    
    if solution:
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
    
    print(f"\n=== Results ===")
    print(f"Total time: {total_time:.3f}s")
    print(f"Total iterations: {iteration}")
    print(f"Total columns: {total_columns}")
    print(f"Best makespan: {best_makespan:.3f}")
    
    # === 生成所有可视化 ===
    print(f"\n=== Generating Visualizations ===")
    
    instance_name = f"Demo_{configs.n_j}x{configs.n_m}"
    
    try:
        # 1. 甘特图
        if best_schedule:
            print("1. Creating Gantt chart...")
            visualizer.draw_gantt_chart(best_schedule, 
                                      f"Demo Schedule (Makespan={best_makespan:.2f})",
                                      instance_name)
            
            # 2. 机器利用率分析
            print("2. Creating machine utilization analysis...")
            visualizer.draw_machine_utilization(best_schedule, 
                                               "Demo Machine Utilization Analysis")
        
        # 3. 收敛曲线
        if len(objective_history) > 1:
            print("3. Creating convergence analysis...")
            visualizer.draw_convergence_curve(objective_history, reduced_cost_history,
                                            "Demo Convergence Analysis", instance_name)
        
        # 4. 创建模拟的多实例汇总（用于演示）
        print("4. Creating summary analysis...")
        demo_results = []
        for i in range(3):
            # 创建模拟结果
            demo_results.append({
                'instance': f'demo_{i+1}',
                'best_makespan': best_makespan + np.random.uniform(-2, 2),
                'time': total_time + np.random.uniform(-1, 1),
                'iterations': iteration + np.random.randint(-2, 3),
                'coverage': 0.95 + np.random.uniform(0, 0.05)
            })
        
        visualizer.draw_column_generation_summary(demo_results)
        
        print("\n✓ All visualizations completed successfully!")
        print(f"✓ Files saved in: {visualizer.save_dir}")
        
        # 显示保存的文件
        import os
        files = os.listdir(visualizer.save_dir)
        print(f"\nGenerated files:")
        for file in sorted(files):
            if file.endswith('.png'):
                print(f"  - {file}")
        
    except Exception as e:
        print(f"✗ Visualization error: {e}")
        import traceback
        traceback.print_exc()
    
    return {
        'schedule': best_schedule,
        'makespan': best_makespan,
        'objective_history': objective_history,
        'reduced_cost_history': reduced_cost_history,
        'total_time': total_time,
        'iterations': iteration
    }

def demo_custom_visualization():
    """
    演示自定义可视化功能
    """
    print("\n=== Custom Visualization Demo ===")
    
    # 创建示例数据
    sample_schedule = [
        {'op_id': 0, 'job_id': 0, 'mch_id': 0, 'start_time': 0, 'end_time': 3, 'processing_time': 3},
        {'op_id': 1, 'job_id': 0, 'mch_id': 1, 'start_time': 3, 'end_time': 7, 'processing_time': 4},
        {'op_id': 2, 'job_id': 1, 'mch_id': 0, 'start_time': 3, 'end_time': 8, 'processing_time': 5},
        {'op_id': 3, 'job_id': 1, 'mch_id': 2, 'start_time': 8, 'end_time': 11, 'processing_time': 3},
        {'op_id': 4, 'job_id': 2, 'mch_id': 1, 'start_time': 7, 'end_time': 12, 'processing_time': 5},
        {'op_id': 5, 'job_id': 2, 'mch_id': 2, 'start_time': 11, 'end_time': 15, 'processing_time': 4},
    ]
    
    sample_objectives = [100, 95, 92, 90, 89, 88.5, 88.2, 88.1, 88.0]
    sample_reduced_costs = [-10, -8, -6, -4, -2, -1, -0.5, -0.1, 0.05]
    
    visualizer = ColumnGenerationVisualizer()
    
    print("Creating custom visualizations...")
    
    # 自定义甘特图
    visualizer.draw_gantt_chart(sample_schedule, "Custom Demo Schedule", "custom_demo")
    
    # 自定义收敛图
    visualizer.draw_convergence_curve(sample_objectives, sample_reduced_costs, 
                                    "Custom Convergence Demo", "custom_demo")
    
    # 自定义机器利用率
    visualizer.draw_machine_utilization(sample_schedule, "Custom Machine Utilization Demo")
    
    print("✓ Custom visualizations completed!")

def main():
    """
    主演示函数
    """
    print("Column Generation Visualization System Demo")
    print("=" * 60)
    
    try:
        # 主要演示
        result = demo_visualization()
        
        # 自定义演示
        demo_custom_visualization()
        
        print("\n" + "=" * 60)
        print("✓ Visualization demo completed successfully!")
        print("Check the 'visualization_results' folder for generated charts.")
        
    except Exception as e:
        print(f"\n✗ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
