#!/usr/bin/env python3
"""
在SD2数据集上验证DRL+分支定价的改进效果
"""

import time
import torch
import numpy as np
import os
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from params import configs
from data_utils import text_to_matrix
from visualization import ImprovementVisualizer, create_improvement_report
from common_utils import heuristic_select_action
import copy

def run_original_drl_sd2(env, model, method='SPT', max_steps=1000):
    """
    在SD2实例上运行原始DRL方法
    """
    start_time = time.time()
    
    # 复制环境
    test_env = copy.deepcopy(env)
    schedule = []
    
    step = 0
    done = False
    
    while not done and step < max_steps:
        # 使用启发式方法选择动作
        action = heuristic_select_action(method, test_env)
        
        # 记录调度决策
        chosen_job = action // test_env.number_of_machines
        chosen_mch = action % test_env.number_of_machines
        chosen_op = test_env.candidate[0, chosen_job]
        
        # 计算时间
        start_op_time = max(test_env.candidate_free_time[0, chosen_job], 
                           test_env.mch_free_time[0, chosen_mch])
        processing_time = test_env.true_op_pt[0, chosen_op, chosen_mch]
        end_op_time = start_op_time + processing_time
        
        schedule.append({
            "op_id": chosen_op,
            "job_id": chosen_job,
            "mch_id": chosen_mch,
            "start_time": start_op_time,
            "end_time": end_op_time,
            "processing_time": processing_time
        })
        
        # 执行动作
        state, reward, done_array = test_env.step(np.array([action]))
        done = done_array[0]
        step += 1
    
    total_time = time.time() - start_time
    makespan = max(task['end_time'] for task in schedule) if schedule else float('inf')
    
    return {
        'makespan': makespan,
        'time': total_time,
        'schedule': schedule,
        'method': f'Original_DRL_{method}'
    }

def run_drl_branch_price_sd2(env, model, column_generator, master_solver, max_iterations=15):
    """
    在SD2实例上运行DRL+分支定价方法
    """
    start_time = time.time()
    
    iteration = 0
    total_columns = 0
    
    while iteration < max_iterations:
        iteration += 1
        
        # 列生成
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=6)
        
        if not new_columns:
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        # 求解主问题
        solution, reduced_cost = master_solver.solve()
        
        # 检查收敛
        if reduced_cost >= -1e-4:
            break
    
    total_time = time.time() - start_time
    
    # 获取最佳调度
    best_makespan = float('inf')
    best_schedule = None
    
    if solution:
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
    
    return {
        'makespan': best_makespan,
        'time': total_time,
        'schedule': best_schedule,
        'iterations': iteration,
        'total_columns': total_columns,
        'method': 'DRL_BranchPrice'
    }

def test_sd2_instance(instance_path):
    """
    测试单个SD2实例
    """
    print(f"Testing: {os.path.basename(instance_path)}")
    
    # 读取实例
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    configs.max_iterations = 15
    configs.cg_topk = 6
    
    # 初始化组件
    model = DANIEL(configs)
    model.eval()
    
    # 创建环境
    env = FJSPEnvForSameOpNums(n_j=n_j, n_m=n_m)
    env.set_initial_data([job_length], [op_pt])
    
    results = {}
    
    # 1. 运行原始DRL方法
    print("  Running Original DRL...")
    heuristic_methods = ['SPT', 'FIFO', 'MOR', 'MWKR']
    best_original = None
    best_original_makespan = float('inf')
    
    for method in heuristic_methods:
        result = run_original_drl_sd2(env, model, method)
        if result['makespan'] < best_original_makespan:
            best_original_makespan = result['makespan']
            best_original = result
    
    results['original'] = best_original
    print(f"    Best Original: {best_original['makespan']:.3f} (time: {best_original['time']:.3f}s)")
    
    # 2. 运行DRL+分支定价方法
    print("  Running DRL + Branch&Price...")
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)
    
    result_bp = run_drl_branch_price_sd2(env, model, column_generator, master_solver)
    results['improved'] = result_bp
    print(f"    DRL+B&P: {result_bp['makespan']:.3f} (time: {result_bp['time']:.3f}s, iters: {result_bp['iterations']})")
    
    # 3. 计算改进效果
    makespan_improvement = (best_original['makespan'] - result_bp['makespan']) / best_original['makespan'] * 100
    time_change = (result_bp['time'] - best_original['time']) / best_original['time'] * 100
    
    print(f"    Improvement: Makespan {makespan_improvement:+.1f}%, Time {time_change:+.1f}%")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'original': best_original,
        'improved': result_bp,
        'makespan_improvement': makespan_improvement,
        'time_change': time_change
    }

def run_sd2_comparison_study():
    """
    运行SD2数据集的完整对比研究
    """
    print("=== SD2 Dataset: DRL vs DRL+Branch&Price Comparison ===")
    
    # 测试不同规模的实例
    test_configs = [
        {'path': './data/SD2/10x5+test', 'count': 10, 'name': '10x5'},
        {'path': './data/SD2/15x10+mix', 'count': 5, 'name': '15x10'},
        {'path': './data/SD2/20x5+mix', 'count': 5, 'name': '20x5'},
    ]
    
    all_results = []
    original_results = {}
    improved_results = {}
    
    for config in test_configs:
        print(f"\n--- Testing {config['name']} instances ---")
        
        if not os.path.exists(config['path']):
            print(f"Path {config['path']} not found, skipping...")
            continue
        
        instance_files = [f for f in os.listdir(config['path']) if f.endswith('.fjs')]
        instance_files.sort()
        
        # 选择前几个实例
        test_instances = instance_files[:config['count']]
        
        for i, instance_file in enumerate(test_instances):
            print(f"\nTest {i+1}/{len(test_instances)} ({config['name']})")
            instance_path = os.path.join(config['path'], instance_file)
            
            try:
                result = test_sd2_instance(instance_path)
                all_results.append(result)
                
                # 为可视化准备数据
                instance_name = f"{config['name']}_{result['instance'][:8]}"
                original_results[instance_name] = {
                    'makespan': result['original']['makespan'],
                    'time': result['original']['time']
                }
                improved_results[instance_name] = {
                    'makespan': result['improved']['makespan'],
                    'time': result['improved']['time']
                }
                
            except Exception as e:
                print(f"  Error: {e}")
                continue
    
    # 汇总结果
    if all_results:
        print(f"\n=== SD2 Comparison Summary ===")
        
        # 按规模分组统计
        size_groups = {}
        for result in all_results:
            size = result['size']
            if size not in size_groups:
                size_groups[size] = []
            size_groups[size].append(result)
        
        print(f"Tested {len(all_results)} instances across {len(size_groups)} different sizes")
        
        # 总体统计
        makespan_improvements = [r['makespan_improvement'] for r in all_results]
        time_changes = [r['time_change'] for r in all_results]
        
        avg_makespan_imp = np.mean(makespan_improvements)
        avg_time_change = np.mean(time_changes)
        best_improvement = max(makespan_improvements)
        instances_improved = sum(1 for x in makespan_improvements if x > 0)
        
        print(f"\nOverall Results:")
        print(f"  Average makespan improvement: {avg_makespan_imp:.1f}%")
        print(f"  Best improvement: {best_improvement:.1f}%")
        print(f"  Success rate: {instances_improved}/{len(all_results)} ({instances_improved/len(all_results)*100:.1f}%)")
        print(f"  Average time change: {avg_time_change:.1f}%")
        
        # 按规模统计
        print(f"\nResults by Problem Size:")
        for size, results in size_groups.items():
            size_makespan_imp = np.mean([r['makespan_improvement'] for r in results])
            size_success_rate = sum(1 for r in results if r['makespan_improvement'] > 0) / len(results)
            print(f"  {size}: {len(results)} instances, avg improvement {size_makespan_imp:.1f}%, success rate {size_success_rate:.1%}")
        
        # 详细结果表
        print(f"\nDetailed Results:")
        print(f"{'Instance':<20} {'Size':<8} {'Orig_MS':<8} {'Impr_MS':<8} {'MS_Imp%':<8} {'Orig_T':<7} {'Impr_T':<7}")
        print("-" * 80)
        for r in all_results:
            print(f"{r['instance'][:19]:<20} {r['size']:<8} {r['original']['makespan']:<8.2f} "
                  f"{r['improved']['makespan']:<8.2f} {r['makespan_improvement']:<8.1f} "
                  f"{r['original']['time']:<7.2f} {r['improved']['time']:<7.2f}")
        
        # 生成可视化报告
        print(f"\n=== Generating SD2 Improvement Analysis ===")
        try:
            # 找到最佳改进的调度用于展示
            best_improvement_result = max(all_results, key=lambda x: x['makespan_improvement'])
            best_schedule = best_improvement_result['improved']['schedule']
            
            create_improvement_report(original_results, improved_results, best_schedule)
            print("✓ SD2 improvement analysis completed!")
            
        except Exception as e:
            print(f"✗ Visualization error: {e}")
    
    return all_results

def main():
    """
    主函数
    """
    print("SD2 Dataset: DRL + Branch&Price Improvement Analysis")
    print("=" * 60)
    
    try:
        results = run_sd2_comparison_study()
        
        if results:
            print("\n" + "=" * 60)
            print("✓ SD2 comparison study completed successfully!")
            print("Key Findings:")
            
            makespan_improvements = [r['makespan_improvement'] for r in results]
            avg_improvement = np.mean(makespan_improvements)
            success_rate = sum(1 for x in makespan_improvements if x > 0) / len(makespan_improvements)
            
            print(f"  • Average makespan improvement: {avg_improvement:.1f}%")
            print(f"  • Success rate: {success_rate:.1%}")
            print(f"  • Best improvement: {max(makespan_improvements):.1f}%")
            print(f"  • Tested across multiple problem sizes")
            print("\nVisualization files saved in './improvement_analysis/' folder")
        else:
            print("✗ No successful tests!")
            
    except Exception as e:
        print(f"\n✗ SD2 comparison failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
