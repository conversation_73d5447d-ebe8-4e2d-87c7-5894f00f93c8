#!/usr/bin/env python3
"""
深入分析为什么两种分支定价方法得到相同结果
"""

import os
import time
import numpy as np
from traditional_branch_price import TraditionalBranchPrice
from exact_branch_and_price import ExactBranchAndPrice
from params import configs
from data_utils import text_to_matrix

def analyze_lp_relaxation_properties(instance_path):
    """
    分析LP松弛的性质
    """
    print(f"\n=== Analyzing LP Relaxation Properties ===")
    print(f"Instance: {os.path.basename(instance_path)}")
    
    # 读取实例
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    
    # 运行传统分支定价并检查变量值
    traditional_solver = TraditionalBranchPrice(configs)
    result = traditional_solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
    
    print(f"\n1. LP Relaxation Analysis:")
    print(f"   Number of columns generated: {len(traditional_solver.columns)}")
    print(f"   Final objective: {result['makespan']:.3f}")
    
    # 检查变量值
    fractional_vars = []
    for i, var in enumerate(traditional_solver.var_list):
        val = var.solution_value()
        print(f"   Variable {i}: {val:.6f}")
        if abs(val - round(val)) > 1e-6:
            fractional_vars.append((i, val))
    
    if fractional_vars:
        print(f"   Found {len(fractional_vars)} fractional variables")
        print("   → LP relaxation solution is NOT integral")
        print("   → Should require branching for exact algorithm")
    else:
        print("   All variables are integral")
        print("   → LP relaxation solution IS integral")
        print("   → No branching needed - this explains why both methods get same result")
    
    # 分析列的质量
    print(f"\n2. Column Quality Analysis:")
    for i, col in enumerate(traditional_solver.columns):
        method = col.get('method', 'unknown')
        makespan = col.get('makespan', 'N/A')
        print(f"   Column {i}: {method}, makespan={makespan}")
    
    return {
        'is_integral': len(fractional_vars) == 0,
        'num_columns': len(traditional_solver.columns),
        'fractional_vars': fractional_vars,
        'makespan': result['makespan']
    }

def test_with_ortools_comparison(instance_path):
    """
    与OR-Tools的精确求解器对比
    """
    print(f"\n=== OR-Tools Comparison ===")
    
    try:
        from ortools.sat.python import cp_model
        
        # 读取实例
        with open(instance_path, 'r') as f:
            lines = f.readlines()
        
        job_length, op_pt = text_to_matrix(lines)
        n_j = len(job_length)
        n_m = op_pt.shape[1]
        n_op = op_pt.shape[0]
        
        # 创建CP-SAT模型
        model = cp_model.CpModel()
        solver = cp_model.CpSolver()
        solver.parameters.max_time_in_seconds = 30.0
        
        # 变量：每个操作的开始时间和机器分配
        op_start = {}
        op_machine = {}
        op_end = {}
        
        # 建立作业-操作映射
        job_op_mapping = {}
        op_id = 0
        for job_id in range(n_j):
            job_op_mapping[job_id] = []
            for op_idx in range(job_length[job_id]):
                job_op_mapping[job_id].append(op_id)
                op_id += 1
        
        # 为每个操作创建变量
        for op_id in range(n_op):
            op_start[op_id] = model.NewIntVar(0, 1000, f'start_{op_id}')
            
            # 找到可用机器
            available_machines = [m for m in range(n_m) if op_pt[op_id, m] > 0]
            if available_machines:
                op_machine[op_id] = model.NewIntVarFromDomain(
                    cp_model.Domain.FromValues(available_machines), f'machine_{op_id}'
                )
                op_end[op_id] = model.NewIntVar(0, 1100, f'end_{op_id}')
                
                # 处理时间约束
                for m in available_machines:
                    pt = int(op_pt[op_id, m])
                    model.Add(op_end[op_id] == op_start[op_id] + pt).OnlyEnforceIf(
                        op_machine[op_id] == m
                    )
        
        # 作业内操作顺序约束
        for job_id in range(n_j):
            job_ops = job_op_mapping[job_id]
            for i in range(len(job_ops) - 1):
                curr_op = job_ops[i]
                next_op = job_ops[i + 1]
                if curr_op in op_end and next_op in op_start:
                    model.Add(op_start[next_op] >= op_end[curr_op])
        
        # 机器容量约束
        for m in range(n_m):
            machine_ops = [op_id for op_id in range(n_op) if op_pt[op_id, m] > 0]
            if len(machine_ops) > 1:
                intervals = []
                for op_id in machine_ops:
                    if op_id in op_machine:
                        interval = model.NewOptionalIntervalVar(
                            op_start[op_id],
                            int(op_pt[op_id, m]),
                            op_end[op_id],
                            op_machine[op_id] == m,
                            f'interval_{op_id}_m{m}'
                        )
                        intervals.append(interval)
                if intervals:
                    model.AddNoOverlap(intervals)
        
        # 目标函数：最小化makespan
        makespan = model.NewIntVar(0, 1100, 'makespan')
        for op_id in range(n_op):
            if op_id in op_end:
                model.Add(makespan >= op_end[op_id])
        
        model.Minimize(makespan)
        
        # 求解
        print("Running OR-Tools CP-SAT solver...")
        start_time = time.time()
        status = solver.Solve(model)
        solve_time = time.time() - start_time
        
        if status == cp_model.OPTIMAL:
            ortools_makespan = solver.Value(makespan)
            print(f"OR-Tools optimal solution: {ortools_makespan:.3f} (time: {solve_time:.3f}s)")
            return ortools_makespan
        elif status == cp_model.FEASIBLE:
            ortools_makespan = solver.Value(makespan)
            print(f"OR-Tools feasible solution: {ortools_makespan:.3f} (time: {solve_time:.3f}s)")
            print("Note: May not be optimal due to time limit")
            return ortools_makespan
        else:
            print(f"OR-Tools failed to find solution (status: {status})")
            return None
            
    except ImportError:
        print("OR-Tools not available for comparison")
        return None
    except Exception as e:
        print(f"OR-Tools comparison failed: {e}")
        return None

def comprehensive_optimality_analysis():
    """
    全面的最优性分析
    """
    print("=== Comprehensive Optimality Analysis ===")
    
    test_instance = "./data/SD2/10x5+test/10x5+test_001.fjs"
    
    if not os.path.exists(test_instance):
        print("Test instance not found!")
        return
    
    # 1. 分析LP松弛性质
    lp_analysis = analyze_lp_relaxation_properties(test_instance)
    
    # 2. 与OR-Tools对比
    ortools_result = test_with_ortools_comparison(test_instance)
    
    # 3. 运行我们的算法
    with open(test_instance, 'r') as f:
        lines = f.readlines()
    job_length, op_pt = text_to_matrix(lines)
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    # 传统分支定价
    traditional_solver = TraditionalBranchPrice(configs)
    trad_result = traditional_solver.solve(job_length, op_pt)
    
    # 精确分支定价
    exact_solver = ExactBranchAndPrice(configs)
    exact_result = exact_solver.solve(job_length, op_pt, time_limit=60)
    
    # 4. 综合分析
    print(f"\n=== FINAL ANALYSIS ===")
    print(f"Traditional B&P: {trad_result['makespan']:.3f}")
    print(f"Exact B&P: {exact_result['makespan']:.3f}")
    if ortools_result:
        print(f"OR-Tools: {ortools_result:.3f}")
    
    print(f"\nLP Relaxation Properties:")
    print(f"  Is integral: {lp_analysis['is_integral']}")
    print(f"  Number of columns: {lp_analysis['num_columns']}")
    
    if lp_analysis['is_integral']:
        print(f"\n✓ EXPLANATION: LP relaxation solution is integral")
        print(f"  → Both branch-and-price methods find the same solution")
        print(f"  → No branching is needed")
        if ortools_result and abs(trad_result['makespan'] - ortools_result) < 1e-3:
            print(f"  → Solution matches OR-Tools optimal solution")
            print(f"  → This IS the optimal solution!")
        elif ortools_result and trad_result['makespan'] > ortools_result + 1e-3:
            print(f"  → Solution is worse than OR-Tools solution")
            print(f"  → Column generation may not have found optimal columns")
    else:
        print(f"\n⚠ UNEXPECTED: LP relaxation solution is fractional")
        print(f"  → Exact branch-and-price should have performed branching")
        print(f"  → There may be a bug in the implementation")

if __name__ == "__main__":
    comprehensive_optimality_analysis()
