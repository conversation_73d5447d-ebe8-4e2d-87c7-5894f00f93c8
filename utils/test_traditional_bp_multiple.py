#!/usr/bin/env python3
"""
测试传统分支定价方法在多个实例上的性能
"""

import os
import time
import numpy as np
from traditional_branch_price import TraditionalBranchPrice
from params import configs
from data_utils import text_to_matrix

def test_multiple_instances():
    """
    在多个实例上测试传统分支定价方法
    """
    print("=== Testing Traditional Branch & Price on Multiple Instances ===")
    
    # 测试配置
    test_configs = [
        {'path': './data/SD2/10x5+test', 'count': 3, 'name': '10x5_SD2'},
        {'path': './data/SD1/10x5', 'count': 3, 'name': '10x5_SD1'},
    ]
    
    all_results = []
    
    for config in test_configs:
        print(f"\n--- Testing {config['name']} instances ---")
        
        if not os.path.exists(config['path']):
            print(f"Path {config['path']} not found, skipping...")
            continue
        
        instance_files = [f for f in os.listdir(config['path']) if f.endswith('.fjs')]
        instance_files.sort()
        
        # 选择前几个实例
        test_instances = instance_files[:config['count']]
        
        for i, instance_file in enumerate(test_instances):
            print(f"\n=== Test {i+1}/{len(test_instances)} ({config['name']}) ===")
            instance_path = os.path.join(config['path'], instance_file)
            
            try:
                result = test_single_instance(instance_path, instance_file)
                result['dataset'] = config['name']
                all_results.append(result)
                
            except Exception as e:
                print(f"  Error: {e}")
                continue
    
    # 汇总分析
    if all_results:
        print(f"\n" + "="*60)
        print(f"=== TRADITIONAL BRANCH & PRICE SUMMARY ===")
        print(f"="*60)
        
        makespans = [r['makespan'] for r in all_results if r['makespan'] != float('inf')]
        times = [r['time'] for r in all_results]
        iterations = [r['iterations'] for r in all_results]
        
        print(f"\nOverall Performance:")
        print(f"  Average makespan: {np.mean(makespans):.2f}")
        print(f"  Average time: {np.mean(times):.3f} seconds")
        print(f"  Average iterations: {np.mean(iterations):.1f}")
        print(f"  Success rate: {len(makespans)}/{len(all_results)}")
        
        # 详细结果表
        print(f"\nDetailed Results:")
        print(f"{'Instance':<25} {'Dataset':<12} {'Makespan':<10} {'Time(s)':<8} {'Iters':<6}")
        print("-" * 70)
        for r in all_results:
            print(f"{r['instance'][:24]:<25} {r['dataset']:<12} {r['makespan']:<10.2f} {r['time']:<8.3f} {r['iterations']:<6}")
    
    return all_results

def test_single_instance(instance_path, instance_name):
    """
    测试单个实例
    """
    print(f"Testing: {instance_name}")
    
    # 读取实例
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    
    # 创建求解器并求解
    start_time = time.time()
    traditional_solver = TraditionalBranchPrice(configs)
    result = traditional_solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
    total_time = time.time() - start_time
    
    result['time'] = total_time
    result['instance'] = instance_name
    
    print(f"  Result: makespan={result['makespan']:.3f}, time={total_time:.3f}s, iters={result['iterations']}")
    
    return result

if __name__ == "__main__":
    results = test_multiple_instances()
    
    if results:
        print(f"\n✓ Traditional Branch & Price testing completed!")
        print(f"  Tested {len(results)} instances successfully")
    else:
        print(f"\n✗ No successful tests!")
