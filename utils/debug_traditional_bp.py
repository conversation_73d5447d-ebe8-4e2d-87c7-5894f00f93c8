#!/usr/bin/env python3
"""
调试传统分支定价方法 - 分析为什么只迭代一次就收敛
"""

import os
import time
import numpy as np
from traditional_branch_price import TraditionalBranchPrice
from params import configs
from data_utils import text_to_matrix

def debug_traditional_bp():
    """
    调试传统分支定价方法
    """
    print("=== Debugging Traditional Branch & Price Method ===")
    
    # 选择测试实例
    test_instance_path = "./data/SD2/10x5+test/10x5+test_001.fjs"
    
    print(f"Testing with instance: {os.path.basename(test_instance_path)}")
    
    # 读取实例数据
    with open(test_instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    print(f"Job lengths: {job_length}")
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    
    # 创建传统分支定价求解器
    print("\nInitializing Traditional Branch & Price solver...")
    traditional_solver = TraditionalBranchPrice(configs)
    
    # 手动执行求解过程以便调试
    print("\nStarting manual solving process for debugging...")
    
    # 初始化主问题
    traditional_solver._initialize_master_problem(job_length, op_pt)
    
    print(f"\nInitial state:")
    print(f"  Number of columns: {len(traditional_solver.columns)}")
    print(f"  Number of operations: {traditional_solver.n_op}")
    
    # 第一次求解主问题
    solution, reduced_cost, obj_value = traditional_solver.solve_master_problem()
    print(f"\nIteration 1:")
    print(f"  Objective value: {obj_value:.3f}")
    print(f"  Returned reduced_cost: {reduced_cost:.5f}")
    print(f"  Number of solution columns: {len(solution) if solution else 0}")
    
    # 获取对偶价格
    dual_values = traditional_solver.get_dual_prices()
    print(f"  Dual prices: {list(dual_values.values())[:10]}...")  # 显示前10个
    print(f"  Min dual price: {min(dual_values.values()):.5f}")
    print(f"  Max dual price: {max(dual_values.values()):.5f}")
    
    # 尝试生成新列
    print(f"\nTrying to generate new columns...")
    new_columns = traditional_solver.generate_columns_traditional(dual_values, max_columns=6)
    print(f"  Generated {len(new_columns)} new columns")
    
    if new_columns:
        for i, col in enumerate(new_columns):
            print(f"    Column {i+1}: makespan={col['makespan']:.3f}, rc={col.get('reduced_cost', 'N/A'):.5f}")
    
    # 分析收敛条件
    print(f"\nConvergence analysis:")
    print(f"  Current reduced_cost: {reduced_cost:.5f}")
    print(f"  Convergence threshold: -1e-4 = {-1e-4:.5f}")
    print(f"  Will converge? {reduced_cost >= -1e-4}")
    
    # 问题分析
    print(f"\n=== PROBLEM ANALYSIS ===")
    print(f"1. 收敛条件问题:")
    print(f"   - 当前代码中 reduced_cost = min(dual_prices.values())")
    print(f"   - 这不是正确的 reduced cost 计算方式")
    print(f"   - 正确的 reduced cost 应该是新生成列的成本减去对偶价格之和")
    
    print(f"\n2. 初始列质量:")
    if solution:
        initial_makespan = solution[0].get('makespan', 'N/A')
        print(f"   - 初始列的makespan: {initial_makespan}")
        print(f"   - 如果初始列已经很好，可能确实不需要更多迭代")
    
    print(f"\n3. 数据集特性:")
    print(f"   - 使用的是SD2数据集，这是合成数据")
    print(f"   - 10x5规模相对较小，可能启发式方法就能找到较好解")

def test_with_different_instances():
    """
    测试不同实例看是否都是一次收敛
    """
    print("\n=== Testing Multiple Instances ===")
    
    test_files = [
        "./data/SD2/10x5+test/10x5+test_001.fjs",
        "./data/SD2/10x5+test/10x5+test_002.fjs",
        "./data/SD2/10x5+test/10x5+test_003.fjs",
    ]
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            continue
            
        print(f"\nTesting: {os.path.basename(test_file)}")
        
        # 读取数据
        with open(test_file, 'r') as f:
            lines = f.readlines()
        
        job_length, op_pt = text_to_matrix(lines)
        
        # 更新配置
        configs.n_j = len(job_length)
        configs.n_m = op_pt.shape[1]
        configs.n_op = op_pt.shape[0]
        
        # 求解
        solver = TraditionalBranchPrice(configs)
        result = solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
        
        print(f"  Result: makespan={result['makespan']:.3f}, iterations={result['iterations']}")

if __name__ == "__main__":
    debug_traditional_bp()
    test_with_different_instances()
