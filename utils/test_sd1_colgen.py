#!/usr/bin/env python3
"""
使用SD1数据集测试列生成系统
"""

import time
import torch
import numpy as np
import os
from model.main_model import DANIEL
from fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from master_solver import MasterSolver
from column_generator import ColumnGenerator
from params import configs
from data_utils import load_data_from_files, text_to_matrix
from visualization import ColumnGenerationVisualizer, create_visualization_report

def test_sd1_instance(instance_path):
    """
    测试单个SD1实例
    """
    print(f"Testing instance: {os.path.basename(instance_path)}")
    
    # 读取实例文件
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    # 解析实例
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Jobs: {n_j}, Machines: {n_m}, Operations: {n_op}")
    print(f"  Job lengths: {job_length}")
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    configs.max_iterations = 20
    configs.cg_topk = 8
    
    # 初始化组件
    model = DANIEL(configs)
    model.eval()
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)
    
    # 初始化环境
    env = FJSPEnvForSameOpNums(n_j=n_j, n_m=n_m)
    env.set_initial_data([job_length], [op_pt])
    
    # 运行列生成
    start_time = time.time()
    iteration = 0
    total_columns = 0
    objective_history = []
    reduced_cost_history = []
    best_schedule_found = None
    
    print(f"  Starting column generation...")
    
    while iteration < configs.max_iterations:
        iteration += 1
        
        # 列生成
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
        
        if not new_columns:
            print(f"    Iteration {iteration}: No new columns - converged!")
            break
            
        total_columns += len(new_columns)
        master_solver.add_columns(new_columns)
        
        # 求解主问题
        solution, reduced_cost = master_solver.solve()
        obj_value = master_solver.solver.Objective().Value()
        objective_history.append(obj_value)
        reduced_cost_history.append(reduced_cost)
        
        if iteration <= 5 or iteration % 5 == 0:
            print(f"    Iteration {iteration}: {len(new_columns)} columns, obj={obj_value:.3f}, rc={reduced_cost:.5f}")
        
        # 检查收敛
        if reduced_cost >= -1e-4:
            print(f"    Iteration {iteration}: Converged (reduced cost >= -1e-4)")
            break
    
    total_time = time.time() - start_time
    
    # 分析结果
    if solution:
        # 找到最优调度
        best_makespan = float('inf')
        best_schedule = None
        for col in solution:
            if 'makespan' in col and col['makespan'] < best_makespan:
                best_makespan = col['makespan']
                best_schedule = col['schedule']
        
        # 验证调度完整性
        if best_schedule:
            scheduled_ops = set(task['op_id'] for task in best_schedule)
            expected_ops = set(range(n_op))
            coverage = len(scheduled_ops & expected_ops) / len(expected_ops)
            best_schedule_found = best_schedule  # 保存最佳调度用于可视化
        else:
            coverage = 0.0
            best_makespan = float('inf')
    else:
        best_makespan = float('inf')
        coverage = 0.0
    
    result = {
        'instance': os.path.basename(instance_path),
        'n_j': n_j,
        'n_m': n_m,
        'n_op': n_op,
        'iterations': iteration,
        'time': total_time,
        'total_columns': total_columns,
        'final_objective': objective_history[-1] if objective_history else float('inf'),
        'best_makespan': best_makespan,
        'coverage': coverage,
        'converged': reduced_cost >= -1e-4 if 'reduced_cost' in locals() else False
    }

    print(f"  Results: {iteration} iters, {total_time:.3f}s, makespan={best_makespan:.3f}, coverage={coverage:.1%}")

    # 添加可视化数据到结果中
    result['objective_history'] = objective_history
    result['reduced_cost_history'] = reduced_cost_history
    result['best_schedule'] = best_schedule_found

    return result

def test_sd1_batch():
    """
    批量测试SD1的10x5实例
    """
    print("=== Testing SD1 10x5 Dataset ===")
    
    # 获取所有10x5实例
    sd1_path = "./data/SD1/10x5"
    if not os.path.exists(sd1_path):
        print(f"Error: SD1 path {sd1_path} not found!")
        return
    
    instance_files = [f for f in os.listdir(sd1_path) if f.endswith('.fjs')]
    instance_files.sort()
    
    print(f"Found {len(instance_files)} instances")
    
    # 测试前几个实例
    test_instances = instance_files[:5]  # 测试前5个实例
    results = []
    
    for i, instance_file in enumerate(test_instances):
        print(f"\n--- Test {i+1}/{len(test_instances)} ---")
        instance_path = os.path.join(sd1_path, instance_file)
        
        try:
            result = test_sd1_instance(instance_path)
            results.append(result)
        except Exception as e:
            print(f"  Error testing {instance_file}: {e}")
            continue
    
    # 汇总结果
    if results:
        print(f"\n=== Summary of {len(results)} tests ===")
        avg_iterations = np.mean([r['iterations'] for r in results])
        avg_time = np.mean([r['time'] for r in results])
        avg_makespan = np.mean([r['best_makespan'] for r in results if r['best_makespan'] != float('inf')])
        avg_coverage = np.mean([r['coverage'] for r in results])
        convergence_rate = np.mean([r['converged'] for r in results])
        
        print(f"Average iterations: {avg_iterations:.1f}")
        print(f"Average time: {avg_time:.3f}s")
        print(f"Average makespan: {avg_makespan:.3f}")
        print(f"Average coverage: {avg_coverage:.1%}")
        print(f"Convergence rate: {convergence_rate:.1%}")
        
        # 详细结果表
        print(f"\nDetailed Results:")
        print(f"{'Instance':<15} {'Iters':<6} {'Time':<8} {'Makespan':<10} {'Coverage':<9} {'Conv':<5}")
        print("-" * 60)
        for r in results:
            conv_str = "Yes" if r['converged'] else "No"
            print(f"{r['instance']:<15} {r['iterations']:<6} {r['time']:<8.3f} {r['best_makespan']:<10.3f} {r['coverage']:<9.1%} {conv_str:<5}")

        # === 生成可视化报告 ===
        print(f"\n=== Generating Visualization Report ===")
        try:
            # 找到最佳结果进行详细可视化
            best_result = min(results, key=lambda x: x['best_makespan'] if x['best_makespan'] != float('inf') else float('inf'))

            if best_result['best_schedule']:
                visualizer = ColumnGenerationVisualizer()

                # 1. 汇总分析
                visualizer.draw_column_generation_summary(results)

                # 2. 最佳调度的详细分析
                instance_name = best_result['instance']
                visualizer.draw_gantt_chart(best_result['best_schedule'],
                                          f"Best Schedule - {instance_name}",
                                          instance_name)

                visualizer.draw_machine_utilization(best_result['best_schedule'],
                                                   f"Machine Utilization - {instance_name}")

                # 3. 收敛分析
                if best_result['objective_history']:
                    visualizer.draw_convergence_curve(best_result['objective_history'],
                                                     best_result['reduced_cost_history'],
                                                     f"Convergence Analysis - {instance_name}",
                                                     instance_name)

                print("✓ Visualization report generated successfully!")
            else:
                print("⚠ No valid schedule found for visualization")

        except Exception as e:
            print(f"✗ Visualization error: {e}")

    return results

def main():
    """
    主测试函数
    """
    print("SD1 Column Generation System Test")
    print("=" * 50)
    
    try:
        results = test_sd1_batch()
        
        if results:
            print("\n" + "=" * 50)
            print("✓ SD1 tests completed successfully!")
            
            # 检查是否所有测试都通过了基本要求
            all_converged = all(r['converged'] for r in results)
            good_coverage = all(r['coverage'] >= 0.8 for r in results)  # 至少80%覆盖率
            
            if all_converged and good_coverage:
                print("✓ All tests passed quality checks!")
            else:
                print("⚠ Some tests did not meet quality requirements:")
                if not all_converged:
                    print("  - Not all instances converged")
                if not good_coverage:
                    print("  - Some instances have low operation coverage")
        else:
            print("✗ No successful tests!")
            
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
