#!/usr/bin/env python3
"""
验证传统分支定价方法解的最优性
"""

import os
import time
import numpy as np
from traditional_branch_price import TraditionalBranchPrice
from params import configs
from data_utils import text_to_matrix

def check_lp_solution_integrality(solver):
    """
    检查LP解是否为整数解
    """
    print("\n=== Checking LP Solution Integrality ===")
    fractional_vars = []
    
    for i, var in enumerate(solver.var_list):
        val = var.solution_value()
        if abs(val - round(val)) > 1e-6:
            fractional_vars.append((i, val))
    
    if fractional_vars:
        print(f"Found {len(fractional_vars)} fractional variables:")
        for i, val in fractional_vars[:5]:  # 显示前5个
            print(f"  Variable {i}: {val:.6f}")
        if len(fractional_vars) > 5:
            print(f"  ... and {len(fractional_vars) - 5} more")
        return False
    else:
        print("✓ All variables have integer values - LP relaxation solution is integral!")
        return True

def compare_with_simple_heuristics(job_length, op_pt):
    """
    与简单启发式方法对比
    """
    print("\n=== Comparing with Simple Heuristics ===")
    
    # 这里可以实现一些简单的启发式方法作为对比
    # 例如：最短处理时间优先、最长处理时间优先等
    
    # 简单的贪心算法：总是选择处理时间最短的可用操作
    def simple_greedy_makespan():
        # 简化实现：估算一个下界
        total_processing_time = np.sum(np.min(op_pt[op_pt > 0].reshape(-1, op_pt.shape[1]), axis=1))
        machine_lower_bound = total_processing_time / op_pt.shape[1]
        return machine_lower_bound
    
    lower_bound = simple_greedy_makespan()
    print(f"Simple lower bound estimate: {lower_bound:.3f}")
    
    return lower_bound

def analyze_problem_structure(job_length, op_pt):
    """
    分析问题结构
    """
    print("\n=== Problem Structure Analysis ===")
    
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"Problem size: {n_j} jobs, {n_m} machines, {n_op} operations")
    print(f"Job lengths: {job_length}")
    
    # 分析处理时间分布
    processing_times = op_pt[op_pt > 0]
    print(f"Processing time statistics:")
    print(f"  Min: {np.min(processing_times):.1f}")
    print(f"  Max: {np.max(processing_times):.1f}")
    print(f"  Mean: {np.mean(processing_times):.1f}")
    print(f"  Std: {np.std(processing_times):.1f}")
    
    # 分析机器兼容性
    compatibility_matrix = (op_pt > 0).astype(int)
    avg_machines_per_op = np.mean(np.sum(compatibility_matrix, axis=1))
    print(f"Average machines per operation: {avg_machines_per_op:.1f}")
    
    # 计算理论下界
    # 机器负载下界：每个操作的最短处理时间之和 / 机器数
    min_processing_times = []
    for op_id in range(n_op):
        op_times = op_pt[op_id][op_pt[op_id] > 0]
        if len(op_times) > 0:
            min_processing_times.append(np.min(op_times))
        else:
            min_processing_times.append(0)

    total_work = np.sum(min_processing_times)
    machine_bound = total_work / n_m

    # 关键路径下界（简化）
    job_min_times = []
    op_idx = 0
    for job_id in range(n_j):
        job_time = 0
        for _ in range(job_length[job_id]):
            if op_idx < n_op:
                op_times = op_pt[op_idx][op_pt[op_idx] > 0]
                if len(op_times) > 0:
                    job_time += np.min(op_times)
                op_idx += 1
        job_min_times.append(job_time)
    
    critical_path_bound = max(job_min_times)
    
    theoretical_lower_bound = max(machine_bound, critical_path_bound)
    print(f"Theoretical lower bounds:")
    print(f"  Machine bound: {machine_bound:.3f}")
    print(f"  Critical path bound: {critical_path_bound:.3f}")
    print(f"  Combined lower bound: {theoretical_lower_bound:.3f}")
    
    return theoretical_lower_bound

def verify_solution_optimality():
    """
    验证解的最优性
    """
    print("=== Verifying Solution Optimality ===")
    
    # 选择测试实例
    test_instance_path = "./data/SD2/10x5+test/10x5+test_001.fjs"
    
    print(f"Testing with instance: {os.path.basename(test_instance_path)}")
    
    # 读取实例数据
    with open(test_instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    # 更新配置
    configs.n_j = n_j
    configs.n_m = n_m
    configs.n_op = n_op
    
    # 分析问题结构
    theoretical_lb = analyze_problem_structure(job_length, op_pt)
    
    # 运行传统分支定价
    print(f"\n=== Running Traditional Branch & Price ===")
    traditional_solver = TraditionalBranchPrice(configs)
    result = traditional_solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
    
    bp_makespan = result['makespan']
    print(f"Branch & Price result: {bp_makespan:.3f}")
    
    # 检查LP解的整数性
    is_integral = check_lp_solution_integrality(traditional_solver)
    
    # 计算最优性差距
    optimality_gap = (bp_makespan - theoretical_lb) / theoretical_lb * 100
    print(f"\n=== Optimality Analysis ===")
    print(f"Theoretical lower bound: {theoretical_lb:.3f}")
    print(f"Branch & Price solution: {bp_makespan:.3f}")
    print(f"Optimality gap: {optimality_gap:.2f}%")
    
    if is_integral:
        print("✓ LP relaxation solution is integral")
        if optimality_gap < 5:  # 5%以内认为是很好的解
            print("✓ Solution appears to be optimal or near-optimal")
        else:
            print("⚠ Large optimality gap - solution may not be optimal")
    else:
        print("⚠ LP relaxation solution is fractional")
        print("⚠ Need branching to find optimal integer solution")
    
    return {
        'makespan': bp_makespan,
        'lower_bound': theoretical_lb,
        'gap': optimality_gap,
        'is_integral': is_integral
    }

def test_multiple_instances():
    """
    在多个实例上验证
    """
    print("\n" + "="*60)
    print("=== Testing Multiple Instances ===")
    
    test_files = [
        "./data/SD2/10x5+test/10x5+test_001.fjs",
        "./data/SD2/10x5+test/10x5+test_002.fjs",
        "./data/SD2/10x5+test/10x5+test_003.fjs",
    ]
    
    results = []
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            continue
            
        print(f"\n--- Testing {os.path.basename(test_file)} ---")
        
        # 读取数据
        with open(test_file, 'r') as f:
            lines = f.readlines()
        
        job_length, op_pt = text_to_matrix(lines)
        
        # 更新配置
        configs.n_j = len(job_length)
        configs.n_m = op_pt.shape[1]
        configs.n_op = op_pt.shape[0]
        
        # 计算理论下界
        theoretical_lb = analyze_problem_structure(job_length, op_pt)
        
        # 求解
        solver = TraditionalBranchPrice(configs)
        result = solver.solve(job_length, op_pt, max_iterations=15, max_columns_per_iter=8)
        
        # 检查整数性
        is_integral = check_lp_solution_integrality(solver)
        
        gap = (result['makespan'] - theoretical_lb) / theoretical_lb * 100
        
        results.append({
            'instance': os.path.basename(test_file),
            'makespan': result['makespan'],
            'lower_bound': theoretical_lb,
            'gap': gap,
            'is_integral': is_integral,
            'iterations': result['iterations']
        })
        
        print(f"Result: makespan={result['makespan']:.3f}, gap={gap:.2f}%, integral={is_integral}")
    
    # 汇总结果
    if results:
        print(f"\n=== Summary ===")
        avg_gap = np.mean([r['gap'] for r in results])
        integral_count = sum(1 for r in results if r['is_integral'])
        
        print(f"Average optimality gap: {avg_gap:.2f}%")
        print(f"Integral solutions: {integral_count}/{len(results)}")
        
        if avg_gap < 5 and integral_count == len(results):
            print("✓ Algorithm appears to find optimal solutions!")
        else:
            print("⚠ Algorithm may not be finding optimal solutions")

if __name__ == "__main__":
    verify_solution_optimality()
    test_multiple_instances()
