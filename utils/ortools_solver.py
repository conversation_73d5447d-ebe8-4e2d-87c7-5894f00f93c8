from ortools.sat.python import cp_model
import numpy as np
import time
import os
from tqdm import tqdm
import sys
from params import configs
from data_utils import pack_data_from_config
import collections

os.environ["CUDA_VISIBLE_DEVICES"] = configs.device_id


def solve_instances(config):
    """
        Solve 'test_data' from 'data_source' using OR-Tools
        with time limits 'max_solve_time' for each instance,
        and save the result to './or_solution/{data_source}'
    :param config: a package of parameters
    :return:
    """


    if not os.path.exists(f'./or_solution/{config.data_source}'):
        os.makedirs(f'./or_solution/{config.data_source}')

    data_list = pack_data_from_config(config.data_source, config.test_data)
    print(f"Loaded {len(data_list)} dataset entries")
    save_direc = f'./or_solution/{config.data_source}'
    if not os.path.exists(save_direc):
        os.makedirs(save_direc)

    for data in data_list:
        dataset = data[0]
        data_name = data[1]
        save_path = save_direc + f'/solution_{data_name}.npy'
        save_subpath = save_direc + f'/{data_name}'

        if not os.path.exists(save_subpath):
            os.makedirs(save_subpath)

        if (not os.path.exists(save_path)) or config.cover_flag:
            print("-" * 25 + "Solve Setting" + "-" * 25)
            print(f"solve data name : {data_name}")
            print(f"path : ./data/{config.data_source}/{data_name}")

            existing_files = sorted([
                f for f in os.listdir(save_subpath)
                if f.startswith(f'solution_{data_name}_') and f.endswith('.npy')
            ])
            index = len(existing_files)

            print(f"left instances: dataset[{index}, {len(dataset[0])})")
            for k in tqdm(range(index, len(dataset[0])), file=sys.stdout, desc="progress", colour='blue'):
                jobs, num_machines = matrix_to_the_format_for_solving(dataset[0][k], dataset[1][k])
                print("\n--- Solving instance %d ---" % (k + 1))
                solution, solve_time = fjsp_solver(jobs=jobs,
                                                   num_machines=num_machines,
                                                   time_limits=config.max_solve_time)
                tqdm.write(f"Instance {k + 1}, Obj:{solution}, Time:{solve_time:.2f}s, System:{time.strftime('%m-%d %H:%M:%S')}")

                # Save solution
                np.save(save_subpath + f'/solution_{data_name}_{str.zfill(str(k + 1), 3)}.npy',
                        np.array([solution, solve_time]))

            print("\nLoading results...")
            results = []
            for i in range(len(dataset[0])):
                solve_msg = np.load(save_subpath + f'/solution_{data_name}_{str.zfill(str(i + 1), 3)}.npy')
                results.append(solve_msg)

            np.save(save_path, np.array(results))
            print("Successfully saved results.")


def matrix_to_the_format_for_solving(job_length, op_pt):
    """
        Convert matrix form of the data into the format needed by OR-Tools
    :param job_length: the number of operations in each job (shape [J])
    :param op_pt: the processing time matrix with shape [N, M],
                where op_pt[i,j] is the processing time of the ith operation
                on the jth machine or 0 if $O_i$ can not process on $M_j$
    :return:
    """
    num_ops, num_machines = op_pt.shape
    num_jobs = job_length.shape[0]
    jobs = []
    op_idx = 0
    for j in range(num_jobs):
        job_msg = []
        for k in range(job_length[j]):
            able_mchs = np.where(op_pt[op_idx] != 0)[0]
            op_msg = [(op_pt[op_idx, k], k) for k in able_mchs]
            job_msg.append(op_msg)
            op_idx += 1
        jobs.append(job_msg)
    return jobs, num_machines


def fjsp_solver(jobs, num_machines, time_limits):
    print(">>> fjsp_solver called.")

    num_jobs = len(jobs)
    all_jobs = range(num_jobs)
    all_machines = range(num_machines)

    model = cp_model.CpModel()

    horizon = sum(max(alt[0] for alt in task) for job in jobs for task in job)

    intervals_per_resources = collections.defaultdict(list)
    starts = {}
    presences = {}
    job_ends = []

    for job_id in all_jobs:
        job = jobs[job_id]
        previous_end = None
        for task_id, task in enumerate(job):
            min_duration = min(alt[0] for alt in task)
            max_duration = max(alt[0] for alt in task)
            start = model.NewIntVar(0, horizon, f'start_j{job_id}_t{task_id}')
            duration = model.NewIntVar(min_duration, max_duration, f'duration_j{job_id}_t{task_id}')
            end = model.NewIntVar(0, horizon, f'end_j{job_id}_t{task_id}')
            interval = model.NewIntervalVar(start, duration, end, f'interval_j{job_id}_t{task_id}')

            starts[(job_id, task_id)] = start
            if previous_end:
                model.Add(start >= previous_end)
            previous_end = end

            if len(task) > 1:
                l_presences = []
                for alt_id, (alt_dur, alt_mach) in enumerate(task):
                    presence = model.NewBoolVar(f'presence_j{job_id}_t{task_id}_a{alt_id}')
                    l_start = model.NewIntVar(0, horizon, f'start_j{job_id}_t{task_id}_a{alt_id}')
                    l_end = model.NewIntVar(0, horizon, f'end_j{job_id}_t{task_id}_a{alt_id}')
                    l_interval = model.NewOptionalIntervalVar(l_start, alt_dur, l_end, presence, f'interval_j{job_id}_t{task_id}_a{alt_id}')
                    model.Add(start == l_start).OnlyEnforceIf(presence)
                    model.Add(duration == alt_dur).OnlyEnforceIf(presence)
                    model.Add(end == l_end).OnlyEnforceIf(presence)
                    intervals_per_resources[alt_mach].append(l_interval)
                    presences[(job_id, task_id, alt_id)] = presence
                    l_presences.append(presence)
                model.AddExactlyOne(l_presences)
            else:
                alt_dur, alt_mach = task[0]
                intervals_per_resources[alt_mach].append(interval)
                presences[(job_id, task_id, 0)] = model.NewConstant(1)

        job_ends.append(previous_end)

    for machine_id in all_machines:
        if len(intervals_per_resources[machine_id]) > 1:
            model.AddNoOverlap(intervals_per_resources[machine_id])

    makespan = model.NewIntVar(0, horizon, 'makespan')
    model.AddMaxEquality(makespan, job_ends)
    model.Minimize(makespan)

    solver = cp_model.CpSolver()
    solver.parameters.max_time_in_seconds = time_limits

    total1 = time.time()
    solution_printer = SolutionPrinter(jobs, starts, presences)
    status = solver.SolveWithSolutionCallback(model, solution_printer)
    total2 = time.time()

    return solver.ObjectiveValue(), total2 - total1

from ortools.sat.python import cp_model

class SolutionPrinter(cp_model.CpSolverSolutionCallback):
    def __init__(self, jobs, starts, presences):
        cp_model.CpSolverSolutionCallback.__init__(self)
        self.jobs = jobs
        self.starts = starts
        self.presences = presences
        self.solution_count = 0

    def on_solution_callback(self):
        print(f"Solution {self.solution_count}, time = {self.WallTime():.2f} s, objective = {self.ObjectiveValue()}")
        for job_id in range(len(self.jobs)):
            print(f'Job {job_id}:')
            for task_id in range(len(self.jobs[job_id])):
                start_value = self.Value(self.starts[(job_id, task_id)])
                machine = -1
                duration = -1
                selected = -1
                for alt_id in range(len(self.jobs[job_id][task_id])):
                    if self.Value(self.presences[(job_id, task_id, alt_id)]):
                        duration = self.jobs[job_id][task_id][alt_id][0]
                        machine = self.jobs[job_id][task_id][alt_id][1]
                        selected = alt_id
                        break
                print(f'  task_{job_id}_{task_id} starts at {start_value} (alt {selected}, machine {machine}, duration {duration})')
        print()
        self.solution_count += 1



if __name__ == '__main__':
    solve_instances(config=configs)
