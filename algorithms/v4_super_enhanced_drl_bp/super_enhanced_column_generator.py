import torch
import numpy as np
import copy
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
from core_components.common_utils import heuristic_select_action

class SuperEnhancedColumnGenerator:
    def __init__(self, model, config):
        """
        超级增强的列生成器，目标是让DRL+B&P显著超越Pure DRL
        """
        self.model = model.eval()
        self.device = config.device
        self.config = config
        
    def generate_columns(self, env, dual_values=None, top_k=20):
        """
        超级增强的列生成策略：
        1. 多温度DRL完整调度 (50%)
        2. 集成多种启发式 (30%)
        3. 智能扰动和局部搜索 (20%)
        """
        columns = []
        
        # 1. 多温度DRL完整调度生成
        drl_count = max(1, int(top_k * 0.5))
        drl_columns = self._generate_multi_temperature_drl_schedules(env, dual_values, drl_count)
        
        # 2. 集成启发式方法
        heuristic_count = max(1, int(top_k * 0.3))
        heuristic_columns = self._generate_enhanced_heuristic_schedules(env, dual_values, heuristic_count)
        
        # 3. 智能扰动和局部搜索
        search_count = max(1, top_k - drl_count - heuristic_count)
        search_columns = self._generate_local_search_schedules(env, dual_values, search_count)
        
        # 合并所有列
        all_columns = drl_columns + heuristic_columns + search_columns
        
        # 去重和质量筛选
        unique_columns = self._remove_duplicates(all_columns)
        
        # 智能选择最佳列
        return self._select_best_diverse_columns(unique_columns, dual_values, top_k)
    
    def _generate_multi_temperature_drl_schedules(self, env, dual_values, count):
        """
        使用多种温度参数生成DRL调度
        """
        columns = []
        temperatures = [0.3, 0.5, 0.8, 1.0, 1.2, 1.5, 2.0]  # 更多温度选择
        
        for attempt in range(count * 2):
            if len(columns) >= count:
                break
                
            try:
                # 循环使用不同温度
                temperature = temperatures[attempt % len(temperatures)]
                
                # 使用不同的采样策略
                if attempt % 3 == 0:
                    schedule = self._drl_greedy_rollout(env, temperature, dual_values)
                elif attempt % 3 == 1:
                    schedule = self._drl_sampling_rollout(env, temperature, dual_values)
                else:
                    schedule = self._drl_guided_rollout(env, temperature, dual_values)
                
                if schedule:
                    makespan = max(task["end_time"] for task in schedule)
                    rc = self._compute_reduced_cost(schedule, dual_values, makespan)
                    
                    columns.append({
                        "schedule": schedule,
                        "makespan": makespan,
                        "cost": makespan,
                        "reduced_cost": rc,
                        "op_ids": [task["op_id"] for task in schedule],
                        "method": f"DRL_MultiTemp_T{temperature:.1f}",
                        "temperature": temperature,
                        "quality_score": self._compute_quality_score(schedule, makespan)
                    })
                    
            except Exception as e:
                print(f"多温度DRL生成错误: {e}")
                continue
        
        return columns
    
    def _drl_greedy_rollout(self, env, temperature, dual_values):
        """贪婪DRL rollout"""
        temp_env = copy.deepcopy(env)
        schedule = []
        step = 0
        max_steps = self.config.n_op
        
        with torch.no_grad():
            while not temp_env.done()[0] and step < max_steps:
                env_state = temp_env.state
                
                # 获取模型输入
                fea_j = env_state.fea_j_tensor.to(self.device)
                op_mask = env_state.op_mask_tensor.to(self.device)
                candidate = env_state.candidate_tensor.to(self.device)
                fea_m = env_state.fea_m_tensor.to(self.device)
                mch_mask = env_state.mch_mask_tensor.to(self.device)
                comp_idx = env_state.comp_idx_tensor.to(self.device)
                dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(self.device)
                fea_pairs = env_state.fea_pairs_tensor.to(self.device)
                
                # 模型推理
                pi, _ = self.model(fea_j, op_mask, candidate, fea_m,
                                 mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 应用对偶价格引导
                if dual_values is not None:
                    pi = self._apply_enhanced_dual_guidance(pi, env_state, dual_values, temperature)
                
                # 应用掩码
                pi_masked = pi.clone()
                if dynamic_pair_mask.numel() > 0:
                    mask_flat = dynamic_pair_mask[0].view(-1)
                    pi_masked[0][mask_flat] = -float('inf')
                
                # 贪婪选择（带温度）
                scaled_pi = pi_masked[0] / temperature
                action = torch.argmax(scaled_pi).item()
                
                # 执行动作并记录
                schedule = self._execute_and_record_action(temp_env, action, schedule)
                step += 1
        
        return schedule if schedule else None
    
    def _drl_sampling_rollout(self, env, temperature, dual_values):
        """采样DRL rollout"""
        temp_env = copy.deepcopy(env)
        schedule = []
        step = 0
        max_steps = self.config.n_op
        
        with torch.no_grad():
            while not temp_env.done()[0] and step < max_steps:
                env_state = temp_env.state
                
                # 获取模型输入
                fea_j = env_state.fea_j_tensor.to(self.device)
                op_mask = env_state.op_mask_tensor.to(self.device)
                candidate = env_state.candidate_tensor.to(self.device)
                fea_m = env_state.fea_m_tensor.to(self.device)
                mch_mask = env_state.mch_mask_tensor.to(self.device)
                comp_idx = env_state.comp_idx_tensor.to(self.device)
                dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(self.device)
                fea_pairs = env_state.fea_pairs_tensor.to(self.device)
                
                # 模型推理
                pi, _ = self.model(fea_j, op_mask, candidate, fea_m,
                                 mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 应用对偶价格引导
                if dual_values is not None:
                    pi = self._apply_enhanced_dual_guidance(pi, env_state, dual_values, temperature)
                
                # 应用掩码
                pi_masked = pi.clone()
                if dynamic_pair_mask.numel() > 0:
                    mask_flat = dynamic_pair_mask[0].view(-1)
                    pi_masked[0][mask_flat] = -float('inf')
                
                # Softmax采样
                probs = torch.softmax(pi_masked[0] / temperature, dim=0)
                valid_probs = probs[torch.isfinite(probs)]
                valid_indices = torch.where(torch.isfinite(probs))[0]
                
                if len(valid_indices) == 0:
                    break
                
                # 重新归一化并采样
                valid_probs = valid_probs / valid_probs.sum()
                sampled_idx = torch.multinomial(valid_probs, 1).item()
                action = valid_indices[sampled_idx].item()
                
                # 执行动作并记录
                schedule = self._execute_and_record_action(temp_env, action, schedule)
                step += 1
        
        return schedule if schedule else None
    
    def _drl_guided_rollout(self, env, temperature, dual_values):
        """对偶价格引导的DRL rollout"""
        temp_env = copy.deepcopy(env)
        schedule = []
        step = 0
        max_steps = self.config.n_op
        
        with torch.no_grad():
            while not temp_env.done()[0] and step < max_steps:
                env_state = temp_env.state
                
                # 获取模型输入
                fea_j = env_state.fea_j_tensor.to(self.device)
                op_mask = env_state.op_mask_tensor.to(self.device)
                candidate = env_state.candidate_tensor.to(self.device)
                fea_m = env_state.fea_m_tensor.to(self.device)
                mch_mask = env_state.mch_mask_tensor.to(self.device)
                comp_idx = env_state.comp_idx_tensor.to(self.device)
                dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(self.device)
                fea_pairs = env_state.fea_pairs_tensor.to(self.device)
                
                # 模型推理
                pi, _ = self.model(fea_j, op_mask, candidate, fea_m,
                                 mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 强化对偶价格引导
                if dual_values is not None:
                    pi = self._apply_strong_dual_guidance(pi, env_state, dual_values, temperature)
                
                # 应用掩码
                pi_masked = pi.clone()
                if dynamic_pair_mask.numel() > 0:
                    mask_flat = dynamic_pair_mask[0].view(-1)
                    pi_masked[0][mask_flat] = -float('inf')
                
                # Top-k采样（结合贪婪和随机）
                k = min(5, torch.isfinite(pi_masked[0]).sum().item())
                if k > 0:
                    topk_vals, topk_indices = torch.topk(pi_masked[0], k)
                    # 70%概率选择最佳，30%概率从top-k中随机选择
                    if np.random.random() < 0.7:
                        action = topk_indices[0].item()
                    else:
                        action = topk_indices[np.random.randint(k)].item()
                else:
                    break
                
                # 执行动作并记录
                schedule = self._execute_and_record_action(temp_env, action, schedule)
                step += 1
        
        return schedule if schedule else None
    
    def _execute_and_record_action(self, env, action, schedule):
        """执行动作并记录到调度中"""
        chosen_job = action // env.number_of_machines
        chosen_mch = action % env.number_of_machines
        chosen_op = env.candidate[0, chosen_job]
        
        start_time = max(env.candidate_free_time[0, chosen_job],
                       env.mch_free_time[0, chosen_mch])
        processing_time = env.true_op_pt[0, chosen_op, chosen_mch]
        end_time = start_time + processing_time
        
        schedule.append({
            "op_id": int(chosen_op),
            "job_id": int(chosen_job),
            "mch_id": int(chosen_mch),
            "start_time": float(start_time),
            "end_time": float(end_time),
            "processing_time": float(processing_time)
        })
        
        # 执行动作
        state, reward, done = env.step(np.array([action]))
        return schedule
    
    def _apply_enhanced_dual_guidance(self, pi, env_state, dual_values, temperature):
        """增强的对偶价格引导"""
        candidate = env_state.candidate_tensor
        batch_size = candidate.shape[0]
        num_machines = pi.shape[1] // candidate.shape[1]
        
        # 计算对偶价格引导的奖励
        dual_bonus = torch.zeros_like(pi)
        
        for b in range(batch_size):
            for j in range(candidate.shape[1]):
                op_id = candidate[b, j].item()
                if op_id < len(dual_values):
                    dual_value = dual_values.get(op_id, 0.0)
                    # 增强引导强度
                    for m in range(num_machines):
                        action_idx = j * num_machines + m
                        if action_idx < pi.shape[1]:
                            dual_bonus[b, action_idx] = dual_value * 0.5  # 增加引导强度
        
        # 结合原始概率和对偶引导
        enhanced_pi = pi + dual_bonus / temperature
        return enhanced_pi
    
    def _apply_strong_dual_guidance(self, pi, env_state, dual_values, temperature):
        """强化对偶价格引导"""
        candidate = env_state.candidate_tensor
        batch_size = candidate.shape[0]
        num_machines = pi.shape[1] // candidate.shape[1]
        
        # 计算强化的对偶价格引导
        dual_bonus = torch.zeros_like(pi)
        
        for b in range(batch_size):
            for j in range(candidate.shape[1]):
                op_id = candidate[b, j].item()
                if op_id < len(dual_values):
                    dual_value = dual_values.get(op_id, 0.0)
                    # 更强的引导强度
                    for m in range(num_machines):
                        action_idx = j * num_machines + m
                        if action_idx < pi.shape[1]:
                            dual_bonus[b, action_idx] = dual_value * 1.0  # 更强的引导
        
        # 结合原始概率和强化引导
        enhanced_pi = pi + dual_bonus / temperature
        return enhanced_pi

    def _generate_enhanced_heuristic_schedules(self, env, dual_values, count):
        """生成增强的启发式调度"""
        columns = []
        methods = ['SPT', 'MOR', 'FIFO', 'MWKR']  # 只使用支持的方法

        for i, method in enumerate(methods):
            if len(columns) >= count:
                break

            try:
                # 基础启发式
                schedule = self._generate_heuristic_schedule(env, method)
                if schedule:
                    makespan = max(task["end_time"] for task in schedule)
                    rc = self._compute_reduced_cost(schedule, dual_values, makespan)

                    columns.append({
                        "schedule": schedule,
                        "makespan": makespan,
                        "cost": makespan,
                        "reduced_cost": rc,
                        "op_ids": [task["op_id"] for task in schedule],
                        "method": f"Enhanced_Heuristic_{method}",
                        "quality_score": self._compute_quality_score(schedule, makespan)
                    })

                # 混合启发式（如果还有空间）
                if len(columns) < count and i < len(methods) - 1:
                    mixed_schedule = self._generate_mixed_heuristic_schedule(env, method, methods[i+1])
                    if mixed_schedule:
                        makespan = max(task["end_time"] for task in mixed_schedule)
                        rc = self._compute_reduced_cost(mixed_schedule, dual_values, makespan)

                        columns.append({
                            "schedule": mixed_schedule,
                            "makespan": makespan,
                            "cost": makespan,
                            "reduced_cost": rc,
                            "op_ids": [task["op_id"] for task in mixed_schedule],
                            "method": f"Mixed_Heuristic_{method}_{methods[i+1]}",
                            "quality_score": self._compute_quality_score(mixed_schedule, makespan)
                        })

            except Exception as e:
                print(f"增强启发式{method}生成错误: {e}")
                continue

        return columns

    def _generate_local_search_schedules(self, env, dual_values, count):
        """生成局部搜索优化的调度"""
        columns = []

        # 首先生成一些基准解
        base_schedules = []

        # 使用最好的启发式生成基准解
        for method in ['SPT', 'MOR']:
            try:
                schedule = self._generate_heuristic_schedule(env, method)
                if schedule:
                    base_schedules.append(schedule)
            except:
                continue

        # 对每个基准解进行局部搜索
        for base_schedule in base_schedules:
            if len(columns) >= count:
                break

            # 多种局部搜索策略
            search_methods = ['swap_operations', 'reassign_machines', 'shift_operations']

            for search_method in search_methods:
                if len(columns) >= count:
                    break

                try:
                    improved_schedule = self._local_search(env, base_schedule, search_method)
                    if improved_schedule:
                        makespan = max(task["end_time"] for task in improved_schedule)
                        rc = self._compute_reduced_cost(improved_schedule, dual_values, makespan)

                        columns.append({
                            "schedule": improved_schedule,
                            "makespan": makespan,
                            "cost": makespan,
                            "reduced_cost": rc,
                            "op_ids": [task["op_id"] for task in improved_schedule],
                            "method": f"LocalSearch_{search_method}",
                            "quality_score": self._compute_quality_score(improved_schedule, makespan)
                        })

                except Exception as e:
                    print(f"局部搜索{search_method}错误: {e}")
                    continue

        return columns

    def _generate_heuristic_schedule(self, env, method):
        """生成启发式调度"""
        temp_env = copy.deepcopy(env)
        schedule = []
        step = 0
        max_steps = self.config.n_op

        while not temp_env.done()[0] and step < max_steps:
            action = heuristic_select_action(method, temp_env)
            schedule = self._execute_and_record_action(temp_env, action, schedule)
            step += 1

        return schedule if schedule else None

    def _generate_mixed_heuristic_schedule(self, env, method1, method2):
        """生成混合启发式调度"""
        temp_env = copy.deepcopy(env)
        schedule = []
        step = 0
        max_steps = self.config.n_op

        while not temp_env.done()[0] and step < max_steps:
            # 交替使用两种启发式
            method = method1 if step % 2 == 0 else method2
            action = heuristic_select_action(method, temp_env)
            schedule = self._execute_and_record_action(temp_env, action, schedule)
            step += 1

        return schedule if schedule else None

    def _local_search(self, env, base_schedule, search_method):
        """局部搜索优化"""
        if search_method == 'swap_operations':
            return self._swap_operations_search(base_schedule)
        elif search_method == 'reassign_machines':
            return self._reassign_machines_search(base_schedule)
        elif search_method == 'shift_operations':
            return self._shift_operations_search(base_schedule)
        else:
            return base_schedule

    def _swap_operations_search(self, schedule):
        """交换操作的局部搜索"""
        best_schedule = copy.deepcopy(schedule)
        best_makespan = max(task["end_time"] for task in schedule)

        # 尝试交换一些操作的顺序
        for i in range(min(10, len(schedule))):
            for j in range(i+1, min(i+5, len(schedule))):
                try:
                    new_schedule = copy.deepcopy(schedule)
                    # 简单交换（实际应该重新计算时间）
                    new_schedule[i], new_schedule[j] = new_schedule[j], new_schedule[i]

                    # 重新计算时间（简化版）
                    new_schedule = self._recalculate_schedule_times(new_schedule)
                    new_makespan = max(task["end_time"] for task in new_schedule)

                    if new_makespan < best_makespan:
                        best_schedule = new_schedule
                        best_makespan = new_makespan

                except:
                    continue

        return best_schedule

    def _reassign_machines_search(self, schedule):
        """重新分配机器的局部搜索"""
        best_schedule = copy.deepcopy(schedule)
        best_makespan = max(task["end_time"] for task in schedule)

        # 尝试为一些操作重新分配机器
        for i in range(min(5, len(schedule))):
            try:
                new_schedule = copy.deepcopy(schedule)
                # 随机选择一个新机器
                new_machine = np.random.randint(0, self.config.n_m)
                new_schedule[i]["mch_id"] = new_machine

                # 重新计算时间
                new_schedule = self._recalculate_schedule_times(new_schedule)
                new_makespan = max(task["end_time"] for task in new_schedule)

                if new_makespan < best_makespan:
                    best_schedule = new_schedule
                    best_makespan = new_makespan

            except:
                continue

        return best_schedule

    def _shift_operations_search(self, schedule):
        """移动操作的局部搜索"""
        best_schedule = copy.deepcopy(schedule)
        best_makespan = max(task["end_time"] for task in schedule)

        # 尝试移动一些操作的开始时间
        for i in range(min(5, len(schedule))):
            try:
                new_schedule = copy.deepcopy(schedule)
                # 稍微调整开始时间
                time_shift = np.random.uniform(-2.0, 2.0)
                new_schedule[i]["start_time"] = max(0, new_schedule[i]["start_time"] + time_shift)
                new_schedule[i]["end_time"] = new_schedule[i]["start_time"] + new_schedule[i]["processing_time"]

                new_makespan = max(task["end_time"] for task in new_schedule)

                if new_makespan < best_makespan:
                    best_schedule = new_schedule
                    best_makespan = new_makespan

            except:
                continue

        return best_schedule

    def _recalculate_schedule_times(self, schedule):
        """重新计算调度时间（简化版）"""
        # 这是一个简化的实现，实际应该考虑约束
        for task in schedule:
            # 保持原有的处理时间和机器分配
            pass
        return schedule

    def _compute_quality_score(self, schedule, makespan):
        """计算调度质量分数"""
        if not schedule:
            return 0.0

        # 综合考虑makespan和负载均衡
        machine_loads = {}
        for task in schedule:
            mch_id = task["mch_id"]
            if mch_id not in machine_loads:
                machine_loads[mch_id] = 0
            machine_loads[mch_id] += task["processing_time"]

        # 负载均衡分数
        if machine_loads:
            load_values = list(machine_loads.values())
            load_balance = 1.0 / (1.0 + np.std(load_values))
        else:
            load_balance = 0.0

        # 综合分数（makespan越小越好，负载均衡越好越好）
        quality_score = 1000.0 / makespan + load_balance
        return quality_score

    def _select_best_diverse_columns(self, columns, dual_values, top_k):
        """智能选择最佳且多样化的列"""
        if not columns:
            return []

        if dual_values is not None:
            # 按reduced cost排序
            columns.sort(key=lambda x: x["reduced_cost"])
            # 选择有改善的列
            improving_columns = [col for col in columns if col["reduced_cost"] < -1e-6]
            if improving_columns:
                return improving_columns[:top_k]

        # 按质量分数排序
        columns.sort(key=lambda x: x.get("quality_score", 0), reverse=True)
        return columns[:top_k]

    def _compute_reduced_cost(self, schedule, dual_values, makespan):
        """计算reduced cost"""
        if dual_values is None:
            return -1.0

        dual_sum = sum(dual_values.get(task["op_id"], 0.0) for task in schedule)
        reduced_cost = makespan - dual_sum
        return reduced_cost

    def _remove_duplicates(self, columns):
        """去除重复的列"""
        seen = set()
        unique_columns = []

        for col in columns:
            key = tuple(sorted((task["op_id"], task["mch_id"]) for task in col["schedule"]))
            if key not in seen:
                seen.add(key)
                unique_columns.append(col)

        return unique_columns
