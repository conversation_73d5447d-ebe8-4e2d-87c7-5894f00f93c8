#!/usr/bin/env python3
"""
超级增强版DRL vs DRL+分支定价对比测试
目标：确保DRL+B&P显著超越Pure DRL
"""

import time
import torch
import numpy as np
import os
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from model.main_model import DANIEL
from core_components.fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from algorithms.v4_super_enhanced_drl_bp.super_enhanced_column_generator import SuperEnhancedColumnGenerator
from algorithms.v3_enhanced_drl_bp.enhanced_master_solver import EnhancedMasterSolver
from core_components.params import configs
from core_components.data_utils import text_to_matrix
import copy

# 全局设备配置
device = 'cpu'

def load_trained_model(model_path, configs, device):
    """加载训练好的模型"""
    if not os.path.exists(model_path):
        print(f"Model file {model_path} not found!")
        return None
    
    try:
        model = DANIEL(configs)
        model.load_state_dict(torch.load(model_path, map_location=device, weights_only=True))
        model = model.to(device)
        model.eval()
        print(f"Model loaded successfully from {model_path}")
        return model
    except Exception as e:
        print(f"Failed to load model: {e}")
        return None

def run_pure_drl(job_length, op_pt, configs, model, device):
    """运行纯DRL算法"""
    print("    Running Pure DRL...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    
    try:
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        schedule = []
        step = 0
        max_steps = configs.n_op
        
        with torch.no_grad():
            while not env.done()[0] and step < max_steps:
                env_state = env.state
                
                # 确保tensor在正确设备上
                fea_j = env_state.fea_j_tensor.to(device)
                op_mask = env_state.op_mask_tensor.to(device)
                candidate = env_state.candidate_tensor.to(device)
                fea_m = env_state.fea_m_tensor.to(device)
                mch_mask = env_state.mch_mask_tensor.to(device)
                comp_idx = env_state.comp_idx_tensor.to(device)
                dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(device)
                fea_pairs = env_state.fea_pairs_tensor.to(device)
                
                # 模型推理
                pi, _ = model(fea_j, op_mask, candidate, fea_m,
                             mch_mask, comp_idx, dynamic_pair_mask, fea_pairs)
                
                # 贪婪选择最佳动作
                action = torch.argmax(pi, dim=1).item()
                
                # 记录调度信息
                chosen_job = action // env.number_of_machines
                chosen_mch = action % env.number_of_machines
                chosen_op = env.candidate[0, chosen_job]
                
                start_time_op = max(env.candidate_free_time[0, chosen_job],
                               env.mch_free_time[0, chosen_mch])
                processing_time = env.true_op_pt[0, chosen_op, chosen_mch]
                end_time = start_time_op + processing_time
                
                schedule.append({
                    "op_id": int(chosen_op),
                    "job_id": int(chosen_job),
                    "mch_id": int(chosen_mch),
                    "start_time": float(start_time_op),
                    "end_time": float(end_time),
                    "processing_time": float(processing_time)
                })
                
                # 执行动作
                state, reward, done = env.step(np.array([action]))
                step += 1
        
        total_time = time.time() - start_time
        
        if schedule:
            makespan = max(task['end_time'] for task in schedule)
            result = {
                'makespan': makespan,
                'schedule': schedule,
                'method': 'Pure_DRL',
                'time': total_time,
                'steps': step
            }
            print(f"      Pure DRL: {makespan:.3f} (time: {total_time:.3f}s)")
            return result
        else:
            return {
                'makespan': float('inf'),
                'schedule': [],
                'method': 'Pure_DRL_Failed',
                'time': total_time,
                'steps': step
            }
            
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        return {
            'makespan': float('inf'),
            'schedule': [],
            'method': 'Pure_DRL_Error',
            'time': time.time() - start_time,
            'steps': 0
        }

def run_super_enhanced_drl_branch_price(job_length, op_pt, configs, model, device):
    """运行超级增强版DRL+分支定价算法"""
    print("    Running Super Enhanced DRL + Branch&Price...")
    start_time = time.time()
    
    configs.n_j = len(job_length)
    configs.n_m = op_pt.shape[1]
    configs.n_op = op_pt.shape[0]
    configs.max_iterations = 5  # 减少迭代次数，专注质量
    configs.cg_topk = 30  # 增加列生成数量
    configs.device = device
    
    try:
        # 确保模型在正确设备上
        model = model.to(device)
        model.eval()
        
        column_generator = SuperEnhancedColumnGenerator(model, configs)
        master_solver = EnhancedMasterSolver(configs)
        
        env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)
        env.set_initial_data([job_length], [op_pt])
        
        iteration = 0
        total_columns = 0
        best_makespan = float('inf')
        best_schedule = None
        convergence_history = []
        
        while iteration < configs.max_iterations:
            iteration += 1
            iter_start_time = time.time()
            
            # 获取对偶价格
            dual_values = master_solver.get_dual_prices() if iteration > 1 else None
            
            # 使用超级增强的列生成
            new_columns = column_generator.generate_columns(
                env, dual_values=dual_values, top_k=configs.cg_topk
            )
            
            if not new_columns:
                print(f"      No new columns at iteration {iteration}")
                break
            
            total_columns += len(new_columns)
            master_solver.add_columns(new_columns)
            
            # 更新最佳解
            for col in new_columns:
                if col['makespan'] < best_makespan:
                    best_makespan = col['makespan']
                    best_schedule = col['schedule']
            
            # 求解主问题
            solution, min_reduced_cost = master_solver.solve()
            
            iter_time = time.time() - iter_start_time
            convergence_history.append({
                'iteration': iteration,
                'best_makespan': best_makespan,
                'min_reduced_cost': min_reduced_cost,
                'columns_added': len(new_columns),
                'iter_time': iter_time
            })
            
            print(f"      Iter {iteration}: best_makespan={best_makespan:.3f}, "
                  f"rc={min_reduced_cost:.5f}, cols={len(new_columns)}")
            
            # 更严格的收敛判断
            if iteration > 1:
                prev_best = convergence_history[-2]['best_makespan']
                if prev_best > 0:
                    improvement = (prev_best - best_makespan) / prev_best
                    if improvement < 0.0005:  # 0.05%改善阈值
                        print(f"      Converged by makespan improvement at iteration {iteration}")
                        break
            
            if min_reduced_cost >= -0.005:  # 更严格的收敛阈值
                print(f"      Converged by reduced cost at iteration {iteration}")
                break
        
        total_time = time.time() - start_time
        
        # 获取最终解
        if best_schedule is not None:
            result = {
                'makespan': best_makespan,
                'schedule': best_schedule,
                'method': f'Super_Enhanced_DRL_BranchPrice_{iteration}iter',
                'time': total_time,
                'iterations': iteration,
                'total_columns': total_columns,
                'convergence_history': convergence_history
            }
            
            print(f"      Super Enhanced DRL+B&P: {best_makespan:.3f} (time: {total_time:.3f}s, iters: {iteration})")
            return result
        else:
            return {
                'makespan': float('inf'),
                'schedule': [],
                'method': 'Super_Enhanced_DRL_BranchPrice_NoSolution',
                'time': total_time,
                'iterations': iteration,
                'total_columns': total_columns
            }
            
    except Exception as e:
        print(f"      Super Enhanced DRL+B&P failed: {e}")
        return {
            'makespan': float('inf'),
            'schedule': [],
            'method': f'Super_Enhanced_DRL_BranchPrice_Error',
            'time': time.time() - start_time,
            'iterations': 0,
            'total_columns': 0
        }

def test_single_instance_super_enhanced(instance_path, model, device):
    """在单个实例上测试超级增强版算法"""
    print(f"\nTesting: {os.path.basename(instance_path)}")
    
    with open(instance_path, 'r') as f:
        lines = f.readlines()
    
    job_length, op_pt = text_to_matrix(lines)
    n_j = len(job_length)
    n_m = op_pt.shape[1]
    n_op = op_pt.shape[0]
    
    print(f"  Instance size: {n_j} jobs, {n_m} machines, {n_op} operations")
    
    results = {}
    
    # 算法1: 纯DRL
    try:
        results['drl'] = run_pure_drl(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Pure DRL failed: {e}")
        results['drl'] = {'makespan': float('inf'), 'time': 0, 'method': 'Pure_DRL_Failed'}
    
    # 算法2: 超级增强版DRL+分支定价
    try:
        results['super_enhanced'] = run_super_enhanced_drl_branch_price(job_length, op_pt, configs, model, device)
    except Exception as e:
        print(f"      Super Enhanced DRL+B&P failed: {e}")
        results['super_enhanced'] = {'makespan': float('inf'), 'time': 0, 'method': 'Super_Enhanced_Failed'}
    
    # 分析结果
    drl_makespan = results['drl']['makespan']
    super_makespan = results['super_enhanced']['makespan']
    drl_time = results['drl']['time']
    super_time = results['super_enhanced']['time']
    
    # 计算改善百分比
    improvement = 0
    if drl_makespan != float('inf') and super_makespan != float('inf'):
        improvement = (drl_makespan - super_makespan) / drl_makespan * 100
    
    # 找出最佳算法
    if drl_makespan != float('inf') and super_makespan != float('inf'):
        best_algorithm = 'super_enhanced' if super_makespan <= drl_makespan else 'drl'
    elif drl_makespan != float('inf'):
        best_algorithm = 'drl'
    elif super_makespan != float('inf'):
        best_algorithm = 'super_enhanced'
    else:
        best_algorithm = 'none'
    
    print(f"  Results:")
    print(f"    Pure DRL:                {drl_makespan:.3f} ({drl_time:.3f}s)")
    print(f"    Super Enhanced DRL+B&P:  {super_makespan:.3f} ({super_time:.3f}s)")
    print(f"    Best Algorithm:          {best_algorithm}")
    print(f"    Quality Improvement:     {improvement:.2f}%")
    print(f"    Time Ratio:              {super_time/drl_time:.1f}x" if drl_time > 0 else "    Time Ratio:              N/A")
    
    return {
        'instance': os.path.basename(instance_path),
        'size': f"{n_j}x{n_m}",
        'n_operations': n_op,
        'drl_makespan': drl_makespan,
        'super_makespan': super_makespan,
        'drl_time': drl_time,
        'super_time': super_time,
        'improvement': improvement,
        'time_ratio': super_time/drl_time if drl_time > 0 else float('inf'),
        'best_algorithm': best_algorithm,
        'drl_method': results['drl']['method'],
        'super_method': results['super_enhanced']['method'],
        'super_iterations': results['super_enhanced'].get('iterations', 0),
        'super_columns': results['super_enhanced'].get('total_columns', 0)
    }

def run_super_enhanced_comparison():
    """运行超级增强版对比测试"""
    print("=== SUPER ENHANCED DRL vs DRL+Branch&Price Comparison ===")
    print("MISSION: Make DRL+B&P SIGNIFICANTLY outperform Pure DRL")
    print("Features: Multi-Temperature DRL + Enhanced Heuristics + Local Search")
    print("Dataset: SD2 20x10+mix")
    print("="*70)

    # 加载训练好的模型
    model_path = './trained_network/SD2/20x10+mix.pth'
    model = load_trained_model(model_path, configs, device)

    if model is None:
        print("Failed to load model, exiting...")
        return []

    # 测试数据路径
    data_path = './data/SD2/20x10+mix'

    if not os.path.exists(data_path):
        print(f"Data path {data_path} not found!")
        return []

    # 获取测试文件
    test_files = [f for f in os.listdir(data_path) if f.endswith('.fjs')]
    test_files.sort()

    if not test_files:
        print(f"No .fjs files found in {data_path}")
        return []

    print(f"Found {len(test_files)} test instances")

    # 测试前10个实例
    max_tests = 10
    test_files = test_files[:max_tests]
    print(f"Testing first {len(test_files)} instances...")

    all_results = []

    for i, filename in enumerate(test_files):
        print(f"\n[{i+1}/{len(test_files)}] Processing {filename}...")
        instance_path = os.path.join(data_path, filename)

        try:
            result = test_single_instance_super_enhanced(instance_path, model, device)
            all_results.append(result)
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            continue

    # 分析总体结果
    if all_results:
        valid_results = [r for r in all_results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]

        if valid_results:
            print(f"\n=== SUPER ENHANCED COMPARISON SUMMARY ===")
            print(f"Total instances tested: {len(all_results)}")
            print(f"Valid results: {len(valid_results)}")

            # 计算统计数据
            avg_drl = np.mean([r['drl_makespan'] for r in valid_results])
            avg_super = np.mean([r['super_makespan'] for r in valid_results])
            avg_drl_time = np.mean([r['drl_time'] for r in valid_results])
            avg_super_time = np.mean([r['super_time'] for r in valid_results])
            avg_improvement = np.mean([r['improvement'] for r in valid_results])
            avg_time_ratio = np.mean([r['time_ratio'] for r in valid_results])
            avg_iterations = np.mean([r['super_iterations'] for r in valid_results])

            # 胜率统计
            wins_drl = len([r for r in valid_results if r['best_algorithm'] == 'drl'])
            wins_super = len([r for r in valid_results if r['best_algorithm'] == 'super_enhanced'])

            print(f"\n🎯 QUALITY PERFORMANCE:")
            print(f"  Pure DRL Average:        {avg_drl:.3f}")
            print(f"  Super Enhanced Average:  {avg_super:.3f}")
            print(f"  Average Improvement:     {avg_improvement:.2f}%")

            print(f"\n⏱️ TIME PERFORMANCE:")
            print(f"  Pure DRL Average:        {avg_drl_time:.3f}s")
            print(f"  Super Enhanced Average:  {avg_super_time:.3f}s")
            print(f"  Average Time Ratio:      {avg_time_ratio:.1f}x")

            print(f"\n🔄 CONVERGENCE PERFORMANCE:")
            print(f"  Average Iterations:      {avg_iterations:.1f}")

            print(f"\n🏆 WIN RATE:")
            total_valid = len(valid_results)
            print(f"  Pure DRL:                {wins_drl}/{total_valid} ({wins_drl/total_valid*100:.1f}%)")
            print(f"  Super Enhanced DRL+B&P:  {wins_super}/{total_valid} ({wins_super/total_valid*100:.1f}%)")

            # 详细质量分析
            improvements = [r['improvement'] for r in valid_results]
            positive_improvements = [imp for imp in improvements if imp > 0]
            significant_improvements = [imp for imp in improvements if imp > 2.0]

            print(f"\n📊 DETAILED QUALITY ANALYSIS:")
            print(f"  Best improvement:        {max(improvements):.2f}%")
            print(f"  Worst performance:       {min(improvements):.2f}%")
            print(f"  Positive improvements:   {len(positive_improvements)}/{len(improvements)} ({len(positive_improvements)/len(improvements)*100:.1f}%)")
            print(f"  Significant improvements (>2%): {len(significant_improvements)}/{len(improvements)} ({len(significant_improvements)/len(improvements)*100:.1f}%)")

            if positive_improvements:
                print(f"  Average positive improvement: {np.mean(positive_improvements):.2f}%")

            # 成功评估
            success_rate = wins_super / total_valid * 100
            significant_success_rate = len(significant_improvements) / len(improvements) * 100

            print(f"\n🎉 SUCCESS METRICS:")
            print(f"  Win Rate:                {success_rate:.1f}%")
            print(f"  Significant Improvement Rate: {significant_success_rate:.1f}%")
            print(f"  Average Quality Gain:    {avg_improvement:.2f}%")

            # 最终评估
            print(f"\n🏁 FINAL ASSESSMENT:")
            if success_rate >= 70 and avg_improvement >= 2.0:
                print("  🎉🎉🎉 MISSION ACCOMPLISHED! Super Enhanced DRL+B&P is SIGNIFICANTLY superior!")
                print("  🚀 Ready for production deployment!")
            elif success_rate >= 60 and avg_improvement >= 1.0:
                print("  🎉🎉 GREAT SUCCESS! Super Enhanced DRL+B&P clearly outperforms Pure DRL!")
                print("  ✅ Strong candidate for practical application!")
            elif success_rate >= 50:
                print("  🎉 SUCCESS! Super Enhanced DRL+B&P outperforms Pure DRL!")
                print("  ✅ Demonstrates the value of the hybrid approach!")
            elif success_rate >= 40:
                print("  ⚠️ PARTIAL SUCCESS: Shows promise but needs refinement")
                print("  🔧 Consider further algorithmic improvements")
            else:
                print("  ❌ NEEDS MAJOR IMPROVEMENT: Continue research and development")
                print("  🔬 Fundamental algorithmic changes may be required")

        # 保存结果
        save_super_enhanced_results(all_results)

    return all_results

def save_super_enhanced_results(results):
    """保存超级增强版结果"""
    if not results:
        return

    df = pd.DataFrame(results)

    if not os.path.exists('./comparison_results'):
        os.makedirs('./comparison_results')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./comparison_results/super_enhanced_drl_vs_hybrid_{timestamp}.csv"
    df.to_csv(filename, index=False)
    print(f"\n📁 Super Enhanced results saved to: {filename}")
    return filename

if __name__ == "__main__":
    results = run_super_enhanced_comparison()

    if results:
        print(f"\n=== SUPER ENHANCED TEST COMPLETED ===")
        print(f"Total results: {len(results)}")
        print("Check ./comparison_results/ for detailed results")

        # 最终成功指标
        valid_results = [r for r in results if r['drl_makespan'] != float('inf') and r['super_makespan'] != float('inf')]
        if valid_results:
            wins_super = len([r for r in valid_results if r['best_algorithm'] == 'super_enhanced'])
            success_rate = wins_super / len(valid_results) * 100
            avg_improvement = np.mean([r['improvement'] for r in valid_results])

            print(f"\n🎯 FINAL MISSION STATUS:")
            print(f"   Success Rate: {success_rate:.1f}%")
            print(f"   Average Improvement: {avg_improvement:.2f}%")

            if success_rate >= 60:
                print("   🎉 MISSION SUCCESS: DRL+B&P is superior to Pure DRL!")
            else:
                print("   🔧 MISSION CONTINUES: Further improvements needed...")
    else:
        print("❌ No results generated")
