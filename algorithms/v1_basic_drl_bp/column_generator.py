import torch
import numpy as np
import copy
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))
from core_components.common_utils import heuristic_select_action

class ColumnGenerator:
    def __init__(self, model, config):
        """
        :param model: 训练好的 DANIEL 策略网络
        :param config: 配置对象
        """
        self.model = model.eval()
        self.device = config.device
        self.config = config

    def generate_columns(self, env, dual_values=None, top_k=10):
        """
        使用 DANIEL 模型获取 top-k 候选动作，然后用贪心算法补全调度
        :param env: FJSP环境实例
        :param dual_values: 可选，主问题提供的对偶值向量
        :param top_k: 候选动作数量
        :return: List of columns，每个元素是完整的调度方案
        """
        columns = []
        generated_schedules = set()  # 用于去重

        # 获取当前环境状态
        env_state = env.state

        with torch.no_grad():
            # 确保所有tensor都在正确的设备上
            device = next(self.model.parameters()).device
            fea_j = env_state.fea_j_tensor.to(device)
            op_mask = env_state.op_mask_tensor.to(device)
            candidate = env_state.candidate_tensor.to(device)
            fea_m = env_state.fea_m_tensor.to(device)
            mch_mask = env_state.mch_mask_tensor.to(device)
            comp_idx = env_state.comp_idx_tensor.to(device)
            dynamic_pair_mask = env_state.dynamic_pair_mask_tensor.to(device)
            fea_pairs = env_state.fea_pairs_tensor.to(device)

            # 模型前向传播，输出每个 (op, mch) 对的概率
            pi, _ = self.model(
                fea_j, op_mask, candidate, fea_m,
                mch_mask, comp_idx, dynamic_pair_mask, fea_pairs
            )

            # 使用 top-k + entropy sampling 策略
            sampled_actions = self._top_k_entropy_sampling(pi, dynamic_pair_mask, top_k, temperature=1.0)

            batch_size = candidate.shape[0]
            num_machines = fea_m.shape[1]

            # 使用多种方法增加多样性
            methods = ['SPT', 'MOR', 'FIFO', 'MWKR']

            for b in range(batch_size):
                attempts = 0
                for action_info in sampled_actions[b]:
                    if len(columns) >= top_k:
                        break

                    flat_idx = action_info['action']
                    op_idx = flat_idx // num_machines
                    mch_id = flat_idx % num_machines
                    prob = action_info['prob']
                    sampling_method = action_info['method']

                    # 检查动作是否有效
                    if (b < dynamic_pair_mask.shape[0] and
                        op_idx < dynamic_pair_mask.shape[1] and
                        mch_id < dynamic_pair_mask.shape[2] and
                        not dynamic_pair_mask[b, op_idx, mch_id]):

                        # 构造动作
                        seed_action = op_idx * num_machines + mch_id

                        # 使用不同的方法增加多样性
                        method = methods[attempts % len(methods)]
                        attempts += 1

                        # 使用种子动作生成完整调度
                        schedule = self._generate_complete_schedule(env, seed_action, method=method)

                        if schedule:
                            # 创建调度的唯一标识符（用于去重）
                            schedule_key = tuple(sorted((task["op_id"], task["mch_id"]) for task in schedule))

                            if schedule_key not in generated_schedules:
                                generated_schedules.add(schedule_key)

                                # 计算调度成本（makespan）
                                makespan = self._compute_schedule_makespan(schedule)

                                # 计算 reduced cost
                                rc = self._compute_reduced_cost(schedule, dual_values, makespan)

                                # 只添加有改善性的列
                                if rc < -1e-6 or dual_values is None:
                                    columns.append({
                                        "schedule": schedule,
                                        "makespan": makespan,
                                        "cost": makespan,
                                        "reduced_cost": rc,
                                        "op_ids": [task["op_id"] for task in schedule],
                                        "seed_action": seed_action,
                                        "seed_score": prob,
                                        "method": method,
                                        "sampling_method": sampling_method
                                    })

            # 按reduced cost排序，返回最好的列
            columns.sort(key=lambda x: x["reduced_cost"])
            return columns[:top_k]

    def _top_k_entropy_sampling(self, pi, dynamic_pair_mask, top_k, temperature=1.0):
        """
        Top-k + Entropy Sampling 策略

        Args:
            pi: 模型输出的概率分布 [batch_size, num_actions]
            dynamic_pair_mask: 动作掩码
            top_k: 采样数量
            temperature: 温度参数，控制探索程度

        Returns:
            List of sampled actions with their probabilities and methods
        """
        batch_size = pi.shape[0]
        sampled_actions = []

        for b in range(batch_size):
            batch_actions = []

            # 1. 获取有效动作的概率
            valid_probs = pi[b].clone()

            # 应用动作掩码
            if b < dynamic_pair_mask.shape[0]:
                mask_flat = dynamic_pair_mask[b].view(-1)
                valid_probs[mask_flat] = -float('inf')  # 无效动作设为负无穷

            # 2. Top-k 贪婪采样 (占50%)
            topk_count = max(1, top_k // 2)
            topk_vals, topk_indices = torch.topk(valid_probs, k=min(topk_count, valid_probs.shape[0]), dim=0)

            for i in range(len(topk_indices)):
                if torch.isfinite(topk_vals[i]):  # 确保是有效动作
                    batch_actions.append({
                        'action': topk_indices[i].item(),
                        'prob': topk_vals[i].item(),
                        'method': 'top_k_greedy'
                    })

            # 3. Entropy Sampling (占50%)
            entropy_count = top_k - len(batch_actions)
            if entropy_count > 0:
                # 应用温度缩放
                scaled_probs = valid_probs / temperature

                # 计算softmax概率
                softmax_probs = torch.softmax(scaled_probs, dim=0)

                # 移除已选择的top-k动作，增加多样性
                remaining_probs = softmax_probs.clone()
                for action_info in batch_actions:
                    remaining_probs[action_info['action']] = 0.0

                # 重新归一化
                if remaining_probs.sum() > 0:
                    remaining_probs = remaining_probs / remaining_probs.sum()

                    # 多项式采样
                    try:
                        sampled_indices = torch.multinomial(
                            remaining_probs,
                            num_samples=min(entropy_count, (remaining_probs > 0).sum().item()),
                            replacement=False
                        )

                        for idx in sampled_indices:
                            batch_actions.append({
                                'action': idx.item(),
                                'prob': softmax_probs[idx].item(),
                                'method': 'entropy_sampling'
                            })
                    except RuntimeError:
                        # 如果采样失败，使用随机选择
                        valid_indices = torch.where(remaining_probs > 0)[0]
                        if len(valid_indices) > 0:
                            random_indices = valid_indices[torch.randperm(len(valid_indices))[:entropy_count]]
                            for idx in random_indices:
                                batch_actions.append({
                                    'action': idx.item(),
                                    'prob': softmax_probs[idx].item(),
                                    'method': 'random_fallback'
                                })

            # 4. 如果还不够，用随机采样补充
            while len(batch_actions) < top_k:
                valid_indices = torch.where(torch.isfinite(valid_probs))[0]
                if len(valid_indices) == 0:
                    break

                # 随机选择一个未选择的有效动作
                used_actions = {action_info['action'] for action_info in batch_actions}
                available_indices = [idx for idx in valid_indices if idx.item() not in used_actions]

                if not available_indices:
                    break

                random_idx = torch.randint(0, len(available_indices), (1,)).item()
                selected_idx = available_indices[random_idx]

                batch_actions.append({
                    'action': selected_idx.item(),
                    'prob': valid_probs[selected_idx].item(),
                    'method': 'random_supplement'
                })

            sampled_actions.append(batch_actions)

        return sampled_actions

    def _generate_complete_schedule(self, env, seed_action, method='SPT'):
        """
        从种子动作开始，用贪心算法生成完整调度
        :param env: FJSP环境
        :param seed_action: 种子动作
        :param method: 贪心方法 ('SPT', 'FIFO', 'MOR', 'MWKR')
        :return: 完整的调度方案
        """
        # 复制环境以避免修改原环境
        temp_env = copy.deepcopy(env)
        schedule = []

        try:
            # 执行种子动作
            state, reward, done = temp_env.step(np.array([seed_action]))

            # 记录第一个调度决策
            chosen_job = seed_action // temp_env.number_of_machines
            chosen_mch = seed_action % temp_env.number_of_machines
            chosen_op = temp_env.candidate[0, chosen_job]  # 假设batch_size=1

            # 计算开始和结束时间
            start_time = max(temp_env.candidate_free_time[0, chosen_job],
                           temp_env.mch_free_time[0, chosen_mch])
            processing_time = temp_env.true_op_pt[0, chosen_op, chosen_mch]
            end_time = start_time + processing_time

            schedule.append({
                "op_id": chosen_op,
                "job_id": chosen_job,
                "mch_id": chosen_mch,
                "start_time": start_time,
                "end_time": end_time,
                "processing_time": processing_time
            })

            # 用贪心算法完成剩余调度
            while not done[0]:
                # 在执行动作前记录当前候选操作
                action = heuristic_select_action(method, temp_env)
                chosen_job = action // temp_env.number_of_machines
                chosen_mch = action % temp_env.number_of_machines
                chosen_op = temp_env.candidate[0, chosen_job]  # 执行前的候选操作

                # 计算时间
                start_time = max(temp_env.candidate_free_time[0, chosen_job],
                               temp_env.mch_free_time[0, chosen_mch])
                processing_time = temp_env.true_op_pt[0, chosen_op, chosen_mch]
                end_time = start_time + processing_time

                # 执行动作
                state, reward, done = temp_env.step(np.array([action]))

                # 记录调度决策
                schedule.append({
                    "op_id": chosen_op,
                    "job_id": chosen_job,
                    "mch_id": chosen_mch,
                    "start_time": start_time,
                    "end_time": end_time,
                    "processing_time": processing_time
                })

            return schedule

        except Exception as e:
            print(f"Error generating schedule: {e}")
            return None

    def _compute_schedule_makespan(self, schedule):
        """
        计算调度方案的makespan
        """
        if not schedule:
            return float('inf')
        return max(task["end_time"] for task in schedule)

    def _compute_reduced_cost(self, schedule, dual_values, makespan):
        """
        改进的reduced cost计算，解决数值异常问题
        """
        if dual_values is None:
            return -1.0  # 如果没有对偶价格，返回负值表示可接受

        # 计算涉及操作的对偶价格之和
        dual_sum = sum(dual_values.get(task["op_id"], 0.0) for task in schedule)

        # 归一化处理，避免数值过大
        num_ops = len(schedule)
        normalized_dual_sum = dual_sum / num_ops if num_ops > 0 else 0

        # 使用归一化的reduced cost计算
        reduced_cost = makespan - normalized_dual_sum

        # 进一步缩放到合理范围
        scaled_rc = reduced_cost / 100.0  # 缩放因子

        return scaled_rc
