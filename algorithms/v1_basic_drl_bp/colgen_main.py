import time
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
#from train_network.SD2 import load_sd2_instance  # 自己写的实例加载函数
#from train_trained_model import load_model       # 模型加载函数
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from model.main_model import DANIEL
from core_components.fjsp_env_same_op_nums import EnvState
from core_components.fjsp_env_same_op_nums import FJSPEnvForSameOpNums
from algorithms.v1_basic_drl_bp.master_solver import MasterSolver
from core_components.params import configs
from algorithms.v1_basic_drl_bp.column_generator import ColumnGenerator
from core_components.visualization import ColumnGenerationVisualizer




def compute_makespan(solution_columns):
    """
    从解中提取最优调度方案并计算makespan
    """
    if not solution_columns:
        return 0

    # 找到最优的调度方案（通常是第一个，因为主问题会选择最优解）
    best_schedule = None
    best_makespan = float('inf')

    for column in solution_columns:
        if 'schedule' in column and 'makespan' in column:
            if column['makespan'] < best_makespan:
                best_makespan = column['makespan']
                best_schedule = column['schedule']

    return best_makespan if best_schedule else 0

def reconstruct_final_schedule(solution_columns):
    """
    从主问题解中重建最终的调度方案
    """
    if not solution_columns:
        return None

    # 简化：选择makespan最小的调度方案
    best_schedule = None
    best_makespan = float('inf')

    for column in solution_columns:
        if 'schedule' in column and 'makespan' in column:
            if column['makespan'] < best_makespan:
                best_makespan = column['makespan']
                best_schedule = column['schedule']

    return best_schedule

def draw_gantt_chart(schedule, title="Gantt Chart"):
    """
    绘制甘特图
    """
    if not schedule:
        print("No schedule to visualize")
        return

    # 获取机器数量和作业数量
    machines = sorted(set(task['mch_id'] for task in schedule))
    jobs = sorted(set(task['job_id'] for task in schedule))

    # 创建颜色映射
    colors = plt.cm.Set3(np.linspace(0, 1, len(jobs)))
    job_colors = {job: colors[i] for i, job in enumerate(jobs)}

    fig, ax = plt.subplots(figsize=(12, 6))

    # 绘制每个任务
    for task in schedule:
        mch_id = task['mch_id']
        job_id = task['job_id']
        start_time = task['start_time']
        duration = task['end_time'] - task['start_time']

        # 绘制矩形
        rect = plt.Rectangle((start_time, mch_id - 0.4), duration, 0.8,
                           facecolor=job_colors[job_id],
                           edgecolor='black', linewidth=1)
        ax.add_patch(rect)

        # 添加操作标签
        ax.text(start_time + duration/2, mch_id, f'J{job_id}O{task["op_id"]}',
                ha='center', va='center', fontsize=8, fontweight='bold')

    # 设置坐标轴
    ax.set_xlim(0, max(task['end_time'] for task in schedule) * 1.1)
    ax.set_ylim(min(machines) - 0.5, max(machines) + 0.5)
    ax.set_xlabel('Time')
    ax.set_ylabel('Machine')
    ax.set_title(title)

    # 设置y轴刻度
    ax.set_yticks(machines)
    ax.set_yticklabels([f'M{m}' for m in machines])

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 添加图例
    legend_elements = [mpatches.Patch(color=job_colors[job], label=f'Job {job}')
                      for job in jobs]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))

    plt.tight_layout()
    plt.show()

    # 保存图片
    plt.savefig('gantt_chart.png', dpi=300, bbox_inches='tight')
    print("Gantt chart saved as 'gantt_chart.png'")
    plt.show()
    plt.close

def main():
    # === 初始化模型与环境 ===
    model = DANIEL(configs)

    # 尝试加载预训练模型（如果存在）
    try:
        model_path = f"./trained_network/{configs.model_source}/{configs.train_size}+{configs.data_suffix}/actor_critic.pth"
        model.load_state_dict(torch.load(model_path, map_location=configs.device))
        print(f"Loaded pre-trained model from {model_path}")
    except:
        print("No pre-trained model found, using randomly initialized model")

    model.eval()  # 设置为评估模式
    column_generator = ColumnGenerator(model, configs)
    master_solver = MasterSolver(configs)

    # 初始化环境（SD1 / SD2）
    env = FJSPEnvForSameOpNums(n_j=configs.n_j, n_m=configs.n_m)

    # 生成一个简单的测试实例
    from core_components.data_utils import SD2_instance_generator
    job_length, op_pt, _ = SD2_instance_generator(configs)
    env.set_initial_data([job_length], [op_pt])

    # 初始化 EnvState（用来与 DANIEL 模型交互）
    env_state = EnvState()
    env_state.update(
        fea_j=env.fea_j,
        op_mask=env.op_mask,
        fea_m=env.fea_m,
        mch_mask=env.mch_mask,
        comp_idx=env.comp_idx,
        dynamic_pair_mask=env.dynamic_pair_mask,
        candidate=env.candidate,
        fea_pairs=env.fea_pairs
    )


    # === 初始化可视化器和记录 ===
    visualizer = ColumnGenerationVisualizer()
    objective_history = []
    reduced_cost_history = []

    # === 开始计时 ===
    total_start = time.time()
    iteration = 0
    total_columns = 0
    max_iterations = configs.max_iterations
    all_columns = []

    print("\n=== Starting Column Generation Process ===")

    while iteration < max_iterations:
        iteration += 1
        print(f"\n>>> Iteration {iteration}")

        # === 列生成 ===
        colgen_start = time.time()
        # 获取对偶价格
        dual_values = master_solver.get_dual_prices() if iteration > 1 else None
        new_columns = column_generator.generate_columns(env, dual_values=dual_values, top_k=configs.cg_topk)
        colgen_time = time.time() - colgen_start
        print(f"[{iteration}] Column Generation Time: {colgen_time:.3f}s")

        if not new_columns:
            print("No new columns generated.")
            break

        total_columns += len(new_columns)
        all_columns.extend(new_columns)
        master_solver.add_columns(new_columns)

        # === 主问题求解 ===
        solve_start = time.time()
        solution, reduced_cost = master_solver.solve()
        solve_time = time.time() - solve_start

        # 记录历史数据
        obj_value = master_solver.solver.Objective().Value()
        objective_history.append(obj_value)
        reduced_cost_history.append(reduced_cost)

        print(f"[{iteration}] Master Problem Solve Time: {solve_time:.3f}s")
        print(f"[{iteration}] Objective: {obj_value:.3f}")
        print(f"[{iteration}] Reduced Cost: {reduced_cost:.5f}")

        # === 收敛判断 ===
        if reduced_cost >= -1e-4:
            print(">>> Converged: reduced cost above threshold.")
            break

    total_time = time.time() - total_start

    # === 评估 ===
    print("\n=== Final Evaluation ===")
    print(f"Total Time: {total_time:.3f}s")
    print(f"Total Iterations: {iteration}")
    print(f"Total Columns Generated: {total_columns}")

    if solution:
        makespan = compute_makespan(solution)
        final_schedule = reconstruct_final_schedule(solution)

        print(f"Final Makespan: {makespan}")
        print(f"Number of selected columns: {len(solution)}")

        if final_schedule:
            print(f"Final schedule contains {len(final_schedule)} operations")
            print("Schedule details:")
            for i, task in enumerate(final_schedule[:5]):  # 显示前5个任务
                print(f"  Task {i+1}: Op{task['op_id']} on Machine{task['mch_id']} "
                      f"[{task['start_time']:.2f}, {task['end_time']:.2f}]")
            if len(final_schedule) > 5:
                print(f"  ... and {len(final_schedule) - 5} more tasks")

            # === 可视化 ===
            try:
                # 生成实例名称
                instance_name = f"{configs.n_j}x{configs.n_m}"

                # 1. 甘特图
                visualizer.draw_gantt_chart(final_schedule,
                                          title=f"Final Schedule (Makespan={makespan:.2f})",
                                          instance_name=instance_name)

                # 2. 机器利用率分析
                visualizer.draw_machine_utilization(final_schedule,
                                                   title="Machine Utilization Analysis")

                # 3. 收敛曲线
                if len(objective_history) > 1:
                    visualizer.draw_convergence_curve(objective_history, reduced_cost_history,
                                                    title="Column Generation Convergence",
                                                    instance_name=instance_name)

                print("All visualizations completed successfully!")

            except Exception as e:
                print(f"Visualization error: {e}")
                import traceback
                traceback.print_exc()

    else:
        print("No valid solution found.")

if __name__ == "__main__":
    main()
